import React, { useState, useEffect } from 'react'
import { Search, TrendingUp, Facebook, Globe, ExternalLink, Heart, MessageCircle, Share, Filter } from 'lucide-react'
import { searchFacebookRecipes, FacebookPost } from '../utils/facebookScraper'
import { searchGoogleRecipes, GoogleSearchResult } from '../utils/googleCrawler'
import { StorageManager, DateUtils } from '../utils/helpers'
import GlassButton from './GlassButton'

interface ViralSearchProps {
  onLoadingChange: (loading: boolean) => void
  onStatsUpdate: () => void
}

interface SearchFilters {
  source: 'all' | 'facebook' | 'google'
  timeframe: 'day' | 'week' | 'month' | 'all'
  minEngagement: number
  maxResults: number
}

const ViralSearch: React.FC<ViralSearchProps> = ({ onLoadingChange, onStatsUpdate }) => {
  const [query, setQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [facebookResults, setFacebookResults] = useState<FacebookPost[]>([])
  const [googleResults, setGoogleResults] = useState<GoogleSearchResult[]>([])
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'facebook' | 'google'>('facebook')
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<SearchFilters>({
    source: 'all',
    timeframe: 'week',
    minEngagement: 50,
    maxResults: 20
  })
  const [recentSearches, setRecentSearches] = useState<string[]>([])

  useEffect(() => {
    loadRecentSearches()
  }, [])

  const loadRecentSearches = async () => {
    try {
      const searches = await StorageManager.getSearchHistory()
      const viralSearches = searches
        .filter(search => search.type === 'viral')
        .map(search => search.query)
        .slice(0, 5)
      setRecentSearches(viralSearches)
    } catch (error) {
      console.error('Failed to load recent searches:', error)
    }
  }

  const handleSearch = async () => {
    if (!query.trim()) {
      setError('Please enter a search query')
      return
    }

    setIsSearching(true)
    setError(null)
    onLoadingChange(true)

    try {
      const keywords = query.split(' ').filter(word => word.length > 2)
      let totalResults = 0

      // Search Facebook if enabled
      if (filters.source === 'all' || filters.source === 'facebook') {
        try {
          const fbResults = await searchFacebookRecipes(keywords, {
            maxResults: Math.floor(filters.maxResults / 2),
            minEngagement: filters.minEngagement,
            dateRange: filters.timeframe !== 'all' ? {
              start: getDateFromTimeframe(filters.timeframe),
              end: new Date()
            } : undefined
          })
          setFacebookResults(fbResults)
          totalResults += fbResults.length
        } catch (error) {
          console.error('Facebook search failed:', error)
        }
      }

      // Search Google if enabled
      if (filters.source === 'all' || filters.source === 'google') {
        try {
          const googleResults = await searchGoogleRecipes(query, {
            maxResults: Math.floor(filters.maxResults / 2),
            dateRange: filters.timeframe !== 'all' ? filters.timeframe : undefined
          })
          setGoogleResults(googleResults)
          totalResults += googleResults.length
        } catch (error) {
          console.error('Google search failed:', error)
        }
      }

      // Save search history
      await StorageManager.saveSearchHistory(query, 'viral', totalResults)
      await StorageManager.updateDailyStats('searched')
      
      onStatsUpdate()
      loadRecentSearches()

      if (totalResults === 0) {
        setError('No viral recipes found. Try different keywords or adjust filters.')
      }
    } catch (error) {
      console.error('Search failed:', error)
      setError(error instanceof Error ? error.message : 'Search failed')
    } finally {
      setIsSearching(false)
      onLoadingChange(false)
    }
  }

  const getDateFromTimeframe = (timeframe: string): Date => {
    const now = new Date()
    switch (timeframe) {
      case 'day':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000)
      case 'week':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      case 'month':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      default:
        return new Date(0)
    }
  }

  const handleSaveRecipe = async (result: FacebookPost | GoogleSearchResult) => {
    try {
      const title = 'title' in result ? result.title : result.recipeTitle || result.content.slice(0, 50)
      const sourceUrl = result.url
      
      await StorageManager.saveRecipe({
        title,
        ingredients: ['Ingredients not available - check source link'],
        source: 'viral',
        sourceUrl,
        tags: ['viral', 'facebook' in result ? 'facebook' : 'google'],
        isFavorite: false,
        notes: `Found via viral search: ${query}`
      })
      
      onStatsUpdate()
    } catch (error) {
      console.error('Failed to save recipe:', error)
    }
  }

  const trendingQueries = [
    'viral pasta recipe',
    'trending chicken dish',
    'popular dessert 2024',
    'easy viral recipe',
    'tiktok famous food'
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center space-x-3 mb-4">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center animate-float">
            <TrendingUp className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold gradient-text">Viral Recipe Search</h1>
            <p className="text-gray-600">Discover trending recipes from Facebook groups and Google</p>
          </div>
        </div>
      </div>

      {/* Search Section */}
      <div className="glass-card p-6">
        <div className="space-y-4">
          <div className="flex space-x-2">
            <div className="flex-1">
              <input
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="e.g., viral pasta recipe, trending dessert"
                className="glass-input w-full text-lg"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                disabled={isSearching}
              />
            </div>
            <GlassButton
              onClick={() => setShowFilters(!showFilters)}
              icon={Filter}
              variant="secondary"
              size="md"
            >
              Filters
            </GlassButton>
            <GlassButton
              onClick={handleSearch}
              disabled={isSearching || !query.trim()}
              icon={Search}
              loading={isSearching}
            >
              {isSearching ? 'Searching...' : 'Search'}
            </GlassButton>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-white/20 rounded-lg border border-white/30">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Source</label>
                <select
                  value={filters.source}
                  onChange={(e) => setFilters(prev => ({ ...prev, source: e.target.value as any }))}
                  className="glass-input w-full"
                >
                  <option value="all">All Sources</option>
                  <option value="facebook">Facebook Only</option>
                  <option value="google">Google Only</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Timeframe</label>
                <select
                  value={filters.timeframe}
                  onChange={(e) => setFilters(prev => ({ ...prev, timeframe: e.target.value as any }))}
                  className="glass-input w-full"
                >
                  <option value="day">Last 24 hours</option>
                  <option value="week">Last week</option>
                  <option value="month">Last month</option>
                  <option value="all">All time</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Min Engagement</label>
                <select
                  value={filters.minEngagement}
                  onChange={(e) => setFilters(prev => ({ ...prev, minEngagement: parseInt(e.target.value) }))}
                  className="glass-input w-full"
                >
                  <option value={10}>10+ interactions</option>
                  <option value={50}>50+ interactions</option>
                  <option value={100}>100+ interactions</option>
                  <option value={500}>500+ interactions</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Max Results</label>
                <select
                  value={filters.maxResults}
                  onChange={(e) => setFilters(prev => ({ ...prev, maxResults: parseInt(e.target.value) }))}
                  className="glass-input w-full"
                >
                  <option value={10}>10 results</option>
                  <option value={20}>20 results</option>
                  <option value={50}>50 results</option>
                </select>
              </div>
            </div>
          )}

          {/* Quick Suggestions */}
          {!query && (
            <div>
              <p className="text-sm text-gray-600 mb-2">Trending searches:</p>
              <div className="flex flex-wrap gap-2">
                {trendingQueries.map((trendingQuery) => (
                  <button
                    key={trendingQuery}
                    onClick={() => setQuery(trendingQuery)}
                    className="px-3 py-1 text-sm bg-gradient-to-r from-orange-100/50 to-red-100/50 hover:from-orange-200/60 hover:to-red-200/60 rounded-full border border-orange-200/50 transition-colors"
                  >
                    🔥 {trendingQuery}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Recent Searches */}
          {recentSearches.length > 0 && !query && (
            <div>
              <p className="text-sm text-gray-600 mb-2">Recent searches:</p>
              <div className="flex flex-wrap gap-2">
                {recentSearches.map((recentSearch, index) => (
                  <button
                    key={index}
                    onClick={() => setQuery(recentSearch)}
                    className="px-3 py-1 text-sm bg-blue-100/50 hover:bg-blue-100/70 rounded-full border border-blue-200/50 transition-colors"
                  >
                    {recentSearch}
                  </button>
                ))}
              </div>
            </div>
          )}

          {error && (
            <div className="p-3 bg-red-100/50 border border-red-200/50 rounded-lg text-red-700 text-sm">
              {error}
            </div>
          )}
        </div>
      </div>

      {/* Results */}
      {(facebookResults.length > 0 || googleResults.length > 0) && (
        <div className="space-y-4">
          {/* Tabs */}
          <div className="flex space-x-2">
            <button
              onClick={() => setActiveTab('facebook')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === 'facebook'
                  ? 'bg-blue-500 text-white'
                  : 'bg-white/20 text-gray-700 hover:bg-white/30'
              }`}
            >
              <Facebook className="w-4 h-4 inline mr-2" />
              Facebook ({facebookResults.length})
            </button>
            <button
              onClick={() => setActiveTab('google')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === 'google'
                  ? 'bg-blue-500 text-white'
                  : 'bg-white/20 text-gray-700 hover:bg-white/30'
              }`}
            >
              <Globe className="w-4 h-4 inline mr-2" />
              Google ({googleResults.length})
            </button>
          </div>

          {/* Facebook Results */}
          {activeTab === 'facebook' && (
            <div className="space-y-4">
              {facebookResults.map((result) => (
                <div key={result.id} className="glass-card p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-800 mb-1">
                        {result.recipeTitle || result.content.slice(0, 60) + '...'}
                      </h3>
                      <p className="text-sm text-gray-600">
                        by {result.author} in {result.groupName}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="px-2 py-1 bg-blue-100/50 text-blue-700 rounded text-xs">
                        {result.confidence}% match
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-gray-700 mb-3 text-sm">{result.content.slice(0, 200)}...</p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <Heart className="w-4 h-4" />
                        <span>{result.engagement.likes}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MessageCircle className="w-4 h-4" />
                        <span>{result.engagement.comments}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Share className="w-4 h-4" />
                        <span>{result.engagement.shares}</span>
                      </div>
                      <span>{DateUtils.formatRelativeTime(result.timestamp)}</span>
                    </div>
                    
                    <div className="flex space-x-2">
                      <GlassButton
                        onClick={() => handleSaveRecipe(result)}
                        size="sm"
                        variant="success"
                      >
                        Save
                      </GlassButton>
                      <GlassButton
                        onClick={() => window.open(result.url, '_blank')}
                        size="sm"
                        variant="secondary"
                        icon={ExternalLink}
                      >
                        View
                      </GlassButton>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Google Results */}
          {activeTab === 'google' && (
            <div className="space-y-4">
              {googleResults.map((result) => (
                <div key={result.url} className="glass-card p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-800 mb-1">{result.title}</h3>
                      <p className="text-sm text-gray-600">{result.source}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="px-2 py-1 bg-green-100/50 text-green-700 rounded text-xs">
                        #{result.rank}
                      </span>
                      <span className="px-2 py-1 bg-blue-100/50 text-blue-700 rounded text-xs">
                        {result.confidence}% match
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-gray-700 mb-3 text-sm">{result.snippet}</p>
                  
                  {result.engagement && (
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                      <span>~{result.engagement.estimatedViews.toLocaleString()} views</span>
                      <span>~{result.engagement.socialShares} shares</span>
                    </div>
                  )}
                  
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">{result.displayUrl}</span>
                    <div className="flex space-x-2">
                      <GlassButton
                        onClick={() => handleSaveRecipe(result)}
                        size="sm"
                        variant="success"
                      >
                        Save
                      </GlassButton>
                      <GlassButton
                        onClick={() => window.open(result.url, '_blank')}
                        size="sm"
                        variant="secondary"
                        icon={ExternalLink}
                      >
                        View
                      </GlassButton>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default ViralSearch
