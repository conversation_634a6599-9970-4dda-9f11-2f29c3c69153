# ViralChef - AI Recipe Generator Chrome Extension

🍳 **ViralChef** is an intelligent Chrome extension that transforms recipe titles into complete ingredient lists using AI and discovers viral recipes from Facebook groups and Google search results.

## ✨ Features

### 🤖 Enhanced AI Recipe Generation
- Transform any recipe title into a complete ingredients list
- **Multiple AI Providers**: OpenAI GPT, DeepSeek, Google Gemini, OpenRouter
- **Advanced Model Selection**: Choose specific models for each provider
- **Temperature Control**: Adjust creativity level (0-1)
- **Token Management**: Control response length and detail
- Customizable preferences (servings, difficulty, dietary restrictions)
- Smart ingredient parsing with measurements and notes
- Real-time AI provider status monitoring

### 🔥 Viral Recipe Discovery
- Search trending recipes from Facebook groups
- Discover popular recipes via Google Custom Search
- Filter by engagement metrics and timeframes
- Save and organize your favorite finds

### 🎨 Enhanced Glassmorphism UI
- **Expanded Sidebar**: Increased to 350px for better UX
- **AI Status Indicator**: Real-time provider connection status
- **Recent Activity**: Track your last recipe generations and searches
- Modern glass-effect design with gradient colors
- Responsive dashboard with smooth animations
- Enhanced hover effects and transitions
- Mobile-friendly interface

### 📚 Enhanced Recipe Management
- Save generated and discovered recipes
- Organize with tags and favorites
- Search through your recipe collection
- Export and share functionality
- **Recent Activity Tracking**: Monitor your recipe generation history
- **Smart Statistics**: Track daily usage and saved recipes

## 🆕 Latest Updates (Enhanced Version)

### Multiple AI Provider Support
- **OpenAI**: GPT-3.5, GPT-4, GPT-4 Turbo, GPT-4o series
- **DeepSeek**: Cost-effective alternative with competitive quality
- **Google Gemini**: Fast and efficient Google AI models
- **OpenRouter**: Access to 20+ models (Claude, GPT, Gemini, Llama, etc.)

### Enhanced User Interface
- **350px Sidebar**: Expanded from 256px for better user experience
- **Real-time AI Status**: Monitor provider connection with visual indicators
- **Recent Activity Panel**: Track your last 10 recipe generations and searches
- **Advanced Settings**: Configure models, temperature, and token limits
- **Smart Validation**: Provider-specific API key validation

### Improved Performance
- **Faster Loading**: Optimized component rendering
- **Better Error Handling**: Comprehensive error messages and fallbacks
- **Enhanced Animations**: Smooth transitions and hover effects

## 🚀 Installation

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd viral-recipe-generator
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Build the extension**
   ```bash
   npm run build
   ```

4. **Load in Chrome**
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked" and select the `dist` folder

### Production Installation
- Install from Chrome Web Store (coming soon)

## ⚙️ Configuration

### Required API Keys

1. **OpenAI API Key** (Required for AI generation)
   - Get yours at [platform.openai.com](https://platform.openai.com/api-keys)
   - Add to extension settings

2. **Google Custom Search** (Optional for Google search)
   - Set up at [Google Custom Search](https://developers.google.com/custom-search)
   - Get API key and Search Engine ID

3. **Facebook Access Token** (Optional for Facebook search)
   - Create app at [Facebook Developers](https://developers.facebook.com)
   - Generate access token with appropriate permissions

### Settings Configuration
- Open the extension dashboard
- Go to Settings tab
- Add your API keys
- Configure search preferences
- Set language and theme preferences

## 🎯 Usage

### Generate Recipe Ingredients
1. Click the extension icon or use the floating button on recipe websites
2. Enter a recipe title (e.g., "Spicy Tuna Pasta")
3. Set preferences (servings, difficulty, dietary restrictions)
4. Click "Generate Ingredients"
5. Save or share the generated recipe

### Search Viral Recipes
1. Open the dashboard and go to "Viral Search"
2. Enter search keywords (e.g., "trending pasta recipe")
3. Apply filters (source, timeframe, engagement)
4. Browse results from Facebook and Google
5. Save interesting recipes to your collection

### Manage Saved Recipes
1. Go to "Saved Recipes" in the dashboard
2. Search and filter your collection
3. View detailed recipe information
4. Mark favorites and add notes
5. Export or share recipes

## 🛠️ Development

### Project Structure
```
/
├── manifest.json          # Chrome extension manifest
├── background.js          # Service worker
├── content.js            # Content script
├── popup.html/js         # Extension popup
├── dashboard/            # React dashboard
├── components/           # React components
├── utils/               # Utility functions
└── assets/              # Icons and images
```

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview built extension
- `npm run build-extension` - Build and copy extension files

### Tech Stack
- **Frontend**: React 18 + TypeScript
- **Styling**: Tailwind CSS with glassmorphism effects
- **Build**: Vite + TypeScript
- **Icons**: Lucide React
- **State**: Zustand
- **i18n**: react-i18next

## 🌍 Internationalization

Currently supported languages:
- 🇺🇸 English (default)
- 🇸🇦 Arabic (العربية)
- 🇫🇷 French (Français)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI for GPT API
- DeepSeek for alternative AI provider
- Lucide for beautiful icons
- Tailwind CSS for styling system
- React team for the amazing framework

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/your-repo/discussions)

---

Made with ❤️ by the ViralChef Team
