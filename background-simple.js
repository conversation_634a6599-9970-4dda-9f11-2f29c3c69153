// ViralChef Extension Background Script - Simple Version
console.log('ViralChef Extension Background Script Loaded');

// Handle action button click to open sidebar
chrome.action.onClicked.addListener(async (tab) => {
  console.log('Extension icon clicked, tab:', tab);

  // Check if the URL is accessible
  if (tab.url.startsWith('chrome://') ||
      tab.url.startsWith('chrome-extension://') ||
      tab.url.startsWith('edge://') ||
      tab.url.startsWith('about:') ||
      tab.url.startsWith('moz-extension://')) {

    console.log('Cannot inject script into protected page:', tab.url);

    // Show notification to user
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'assets/icons/icon48.png',
      title: 'ViralChef',
      message: 'Cannot open sidebar on this page. Please visit a regular website (like google.com) and try again.'
    });

    // Open dashboard instead
    chrome.tabs.create({
      url: chrome.runtime.getURL('dashboard/index.html')
    });
    return;
  }

  try {
    console.log('Attempting to inject script into:', tab.url);

    // Simple test - just inject an alert first
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: () => {
        console.log('Script injected successfully!');
        
        // Remove existing sidebar if any
        const existing = document.getElementById('viralchef-test-sidebar');
        if (existing) {
          existing.remove();
          return;
        }
        
        // Create simple test sidebar
        const sidebar = document.createElement('div');
        sidebar.id = 'viralchef-test-sidebar';
        sidebar.style.cssText = `
          position: fixed !important;
          top: 0 !important;
          left: 0 !important;
          width: 300px !important;
          height: 100vh !important;
          background: #1a1a1a !important;
          border-right: 2px solid #dc2626 !important;
          z-index: 999999 !important;
          color: white !important;
          padding: 20px !important;
          box-sizing: border-box !important;
          font-family: Arial, sans-serif !important;
          transform: translateX(-100%) !important;
          transition: transform 0.5s ease !important;
        `;
        
        sidebar.innerHTML = `
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="color: #dc2626; margin: 0;">🍳 ViralChef</h2>
            <p style="margin: 10px 0; font-size: 14px;">AI Recipe Generator</p>
          </div>
          
          <div style="margin-bottom: 20px;">
            <input type="text" placeholder="Enter recipe title..." style="
              width: 100%;
              padding: 10px;
              border: 1px solid #dc2626;
              border-radius: 5px;
              background: #2d1b1b;
              color: white;
              box-sizing: border-box;
            ">
          </div>
          
          <button onclick="alert('Generate clicked!')" style="
            width: 100%;
            padding: 12px;
            background: #dc2626;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
          ">Generate Recipe</button>
          
          <button onclick="document.getElementById('viralchef-test-sidebar').remove()" style="
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            background: #dc2626;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
          ">×</button>
        `;
        
        document.body.appendChild(sidebar);
        
        // Animate in
        setTimeout(() => {
          sidebar.style.transform = 'translateX(0)';
        }, 100);
        
        console.log('Test sidebar created successfully!');
      }
    });
    
    console.log('Script injection completed');
    
  } catch (error) {
    console.error('Failed to inject script:', error);
    console.error('Error details:', error.message);
  }
});

// Handle messages from content script or popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Message received:', request);
  
  if (request.action === 'openDashboard') {
    chrome.tabs.create({
      url: chrome.runtime.getURL('dashboard/index.html')
    });
  }
  
  sendResponse({ success: true });
});
