import React, { useState, useEffect } from 'react'
import { BookOpen, Search, Heart, ExternalLink, Trash2, Calendar } from 'lucide-react'
import { StorageManager, SavedRecipe, DateUtils } from '../utils/helpers'
import GlassButton from './GlassButton'

interface SavedRecipesProps {
  onStatsUpdate: () => void
}

const SavedRecipes: React.FC<SavedRecipesProps> = ({ onStatsUpdate }) => {
  const [recipes, setRecipes] = useState<SavedRecipe[]>([])
  const [filteredRecipes, setFilteredRecipes] = useState<SavedRecipe[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'generated' | 'viral' | 'favorites'>('all')
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'title' | 'difficulty'>('newest')
  const [isLoading, setIsLoading] = useState(true)
  const [selectedRecipe, setSelectedRecipe] = useState<SavedRecipe | null>(null)

  useEffect(() => {
    loadRecipes()
  }, [])

  useEffect(() => {
    filterAndSortRecipes()
  }, [recipes, searchQuery, selectedFilter, sortBy])

  const loadRecipes = async () => {
    try {
      setIsLoading(true)
      const savedRecipes = await StorageManager.getRecipes()
      setRecipes(savedRecipes)
    } catch (error) {
      console.error('Failed to load recipes:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filterAndSortRecipes = () => {
    let filtered = recipes

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(recipe =>
        recipe.title.toLowerCase().includes(query) ||
        recipe.ingredients.some(ingredient => ingredient.toLowerCase().includes(query)) ||
        recipe.tags.some(tag => tag.toLowerCase().includes(query))
      )
    }

    // Apply category filter
    switch (selectedFilter) {
      case 'generated':
        filtered = filtered.filter(recipe => recipe.source === 'generated')
        break
      case 'viral':
        filtered = filtered.filter(recipe => recipe.source === 'viral')
        break
      case 'favorites':
        filtered = filtered.filter(recipe => recipe.isFavorite)
        break
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        case 'title':
          return a.title.localeCompare(b.title)
        case 'difficulty':
          const difficultyOrder = { 'Easy': 1, 'Medium': 2, 'Hard': 3 }
          return (difficultyOrder[a.difficulty || 'Medium'] || 2) - (difficultyOrder[b.difficulty || 'Medium'] || 2)
        default:
          return 0
      }
    })

    setFilteredRecipes(filtered)
  }

  const handleDeleteRecipe = async (id: string) => {
    if (confirm('Are you sure you want to delete this recipe?')) {
      try {
        await StorageManager.deleteRecipe(id)
        setRecipes(prev => prev.filter(recipe => recipe.id !== id))
        onStatsUpdate()
      } catch (error) {
        console.error('Failed to delete recipe:', error)
      }
    }
  }

  const handleToggleFavorite = async (id: string) => {
    try {
      const recipe = recipes.find(r => r.id === id)
      if (recipe) {
        await StorageManager.updateRecipe(id, { isFavorite: !recipe.isFavorite })
        setRecipes(prev => prev.map(r => 
          r.id === id ? { ...r, isFavorite: !r.isFavorite } : r
        ))
      }
    } catch (error) {
      console.error('Failed to update favorite:', error)
    }
  }

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'generated':
        return '🤖'
      case 'viral':
        return '🔥'
      default:
        return '📝'
    }
  }

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'Easy':
        return 'text-green-600 bg-green-100/50'
      case 'Medium':
        return 'text-yellow-600 bg-yellow-100/50'
      case 'Hard':
        return 'text-red-600 bg-red-100/50'
      default:
        return 'text-gray-600 bg-gray-100/50'
    }
  }

  if (isLoading) {
    return (
      <div className="glass-card p-8 text-center">
        <div className="loading-spinner mx-auto mb-4"></div>
        <p className="text-gray-600">Loading your saved recipes...</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center space-x-3 mb-4">
          <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center animate-float">
            <BookOpen className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold gradient-text">Saved Recipes</h1>
            <p className="text-gray-600">Your personal recipe collection</p>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="glass-card p-4">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search recipes, ingredients, or tags..."
                className="glass-input w-full pl-10"
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex space-x-2">
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value as any)}
              className="glass-input"
            >
              <option value="all">All Recipes</option>
              <option value="generated">AI Generated</option>
              <option value="viral">Viral Finds</option>
              <option value="favorites">Favorites</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="glass-input"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="title">Alphabetical</option>
              <option value="difficulty">By Difficulty</option>
            </select>
          </div>
        </div>

        {/* Stats */}
        <div className="flex justify-between items-center mt-4 pt-4 border-t border-white/20">
          <div className="flex space-x-6 text-sm text-gray-600">
            <span>Total: {recipes.length}</span>
            <span>Generated: {recipes.filter(r => r.source === 'generated').length}</span>
            <span>Viral: {recipes.filter(r => r.source === 'viral').length}</span>
            <span>Favorites: {recipes.filter(r => r.isFavorite).length}</span>
          </div>
          <span className="text-sm text-gray-600">
            Showing {filteredRecipes.length} recipes
          </span>
        </div>
      </div>

      {/* Recipe Grid */}
      {filteredRecipes.length === 0 ? (
        <div className="glass-card p-8 text-center">
          <BookOpen className="w-16 h-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No recipes found</h3>
          <p className="text-gray-600">
            {searchQuery || selectedFilter !== 'all' 
              ? 'Try adjusting your search or filters'
              : 'Start by generating or finding some viral recipes!'
            }
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRecipes.map((recipe) => (
            <div key={recipe.id} className="glass-card p-4 hover:scale-105 transition-transform">
              {/* Recipe Header */}
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-800 mb-1 line-clamp-2">
                    {recipe.title}
                  </h3>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <span>{getSourceIcon(recipe.source)}</span>
                    <span className="capitalize">{recipe.source}</span>
                    {recipe.difficulty && (
                      <span className={`px-2 py-1 rounded text-xs ${getDifficultyColor(recipe.difficulty)}`}>
                        {recipe.difficulty}
                      </span>
                    )}
                  </div>
                </div>
                <button
                  onClick={() => handleToggleFavorite(recipe.id)}
                  className={`p-1 rounded-full transition-colors ${
                    recipe.isFavorite 
                      ? 'text-red-500 hover:text-red-600' 
                      : 'text-gray-400 hover:text-red-500'
                  }`}
                >
                  <Heart className={`w-5 h-5 ${recipe.isFavorite ? 'fill-current' : ''}`} />
                </button>
              </div>

              {/* Recipe Info */}
              <div className="space-y-2 mb-4">
                {recipe.prepTime && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Calendar className="w-4 h-4" />
                    <span>{recipe.prepTime}</span>
                    {recipe.servings && <span>• {recipe.servings} servings</span>}
                  </div>
                )}
                
                <div className="text-sm text-gray-600">
                  {recipe.ingredients.length} ingredients
                </div>

                {recipe.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {recipe.tags.slice(0, 3).map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-blue-100/50 text-blue-700 rounded text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                    {recipe.tags.length > 3 && (
                      <span className="px-2 py-1 bg-gray-100/50 text-gray-600 rounded text-xs">
                        +{recipe.tags.length - 3}
                      </span>
                    )}
                  </div>
                )}
              </div>

              {/* Recipe Actions */}
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">
                  {DateUtils.formatRelativeTime(recipe.createdAt)}
                </span>
                
                <div className="flex space-x-1">
                  <GlassButton
                    onClick={() => setSelectedRecipe(recipe)}
                    size="sm"
                    variant="primary"
                  >
                    View
                  </GlassButton>
                  
                  {recipe.sourceUrl && (
                    <GlassButton
                      onClick={() => window.open(recipe.sourceUrl, '_blank')}
                      size="sm"
                      variant="secondary"
                      icon={ExternalLink}
                    >
                      Link
                    </GlassButton>
                  )}
                  
                  <button
                    onClick={() => handleDeleteRecipe(recipe.id)}
                    className="p-2 text-red-500 hover:text-red-600 hover:bg-red-100/20 rounded-lg transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Recipe Detail Modal */}
      {selectedRecipe && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="glass-card max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-2">
                    {selectedRecipe.title}
                  </h2>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span>{getSourceIcon(selectedRecipe.source)} {selectedRecipe.source}</span>
                    {selectedRecipe.difficulty && (
                      <span className={`px-2 py-1 rounded ${getDifficultyColor(selectedRecipe.difficulty)}`}>
                        {selectedRecipe.difficulty}
                      </span>
                    )}
                    {selectedRecipe.prepTime && <span>⏱️ {selectedRecipe.prepTime}</span>}
                    {selectedRecipe.servings && <span>👥 {selectedRecipe.servings} servings</span>}
                  </div>
                </div>
                <button
                  onClick={() => setSelectedRecipe(null)}
                  className="text-gray-500 hover:text-gray-700 text-2xl"
                >
                  ×
                </button>
              </div>

              {/* Ingredients */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-3">Ingredients</h3>
                <div className="space-y-2">
                  {selectedRecipe.ingredients.map((ingredient, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-3 p-2 bg-white/20 rounded-lg"
                    >
                      <div className="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                        {index + 1}
                      </div>
                      <span>{ingredient}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Tags */}
              {selectedRecipe.tags.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-gray-700 mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedRecipe.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-blue-100/50 text-blue-700 rounded text-sm"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Notes */}
              {selectedRecipe.notes && (
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-gray-700 mb-2">Notes</h4>
                  <p className="text-gray-600 text-sm">{selectedRecipe.notes}</p>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-between items-center pt-4 border-t border-white/20">
                <span className="text-xs text-gray-500">
                  Created {DateUtils.formatRelativeTime(selectedRecipe.createdAt)}
                </span>
                
                <div className="flex space-x-2">
                  {selectedRecipe.sourceUrl && (
                    <GlassButton
                      onClick={() => window.open(selectedRecipe.sourceUrl, '_blank')}
                      icon={ExternalLink}
                      variant="secondary"
                    >
                      View Source
                    </GlassButton>
                  )}
                  <GlassButton
                    onClick={() => handleToggleFavorite(selectedRecipe.id)}
                    icon={Heart}
                    variant={selectedRecipe.isFavorite ? 'danger' : 'success'}
                  >
                    {selectedRecipe.isFavorite ? 'Remove Favorite' : 'Add Favorite'}
                  </GlassButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default SavedRecipes
