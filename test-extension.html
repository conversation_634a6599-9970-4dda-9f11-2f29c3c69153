<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ViralChef Extension - Test Interface</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #10b981, #3b82f6, #8b5cf6);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        .extension-popup {
            width: 400px;
            height: 600px;
            background: linear-gradient(135deg, #1a1a1a, #2d1b1b);
            border-radius: 16px;
            border: 2px solid rgba(220, 38, 38, 0.3);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            color: white;
            overflow: hidden;
        }
        
        .ai-status {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 16px 20px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            font-size: 11px;
            transition: all 0.3s ease;
        }
        
        .ai-status.connected {
            background: rgba(34, 197, 94, 0.1);
            border-color: rgba(34, 197, 94, 0.3);
            color: #22c55e;
        }
        
        .ai-status.error {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }
        
        .glass-button {
            background: linear-gradient(135deg, rgba(220, 38, 38, 0.8), rgba(127, 29, 29, 0.8));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(220, 38, 38, 0.3);
            border-radius: 12px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            width: 100%;
        }
        
        .glass-button:hover {
            background: rgba(220, 38, 38, 1);
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
        }
        
        .glass-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 10px 12px;
            color: white;
            width: 100%;
            font-size: 14px;
        }
        
        .glass-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .provider-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 16px;
            margin: 8px 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .provider-card:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: scale(1.02);
        }
        
        .provider-card.active {
            background: rgba(34, 197, 94, 0.1);
            border-color: rgba(34, 197, 94, 0.3);
        }
    </style>
</head>
<body class="p-8">
    <div class="max-w-6xl mx-auto">
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-4">ViralChef Extension - Enhanced Version</h1>
            <p class="text-xl text-white/80">Test the new features and multiple AI provider support</p>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Extension Popup Simulation -->
            <div class="lg:col-span-1">
                <h2 class="text-2xl font-bold text-white mb-4">Extension Popup</h2>
                <div class="extension-popup">
                    <!-- Header -->
                    <div class="text-center p-6 border-b border-white/10">
                        <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-r from-red-600 to-red-800 rounded-xl flex items-center justify-center text-2xl">
                            🍳
                        </div>
                        <h3 class="text-lg font-bold">ViralChef</h3>
                        <p class="text-sm text-gray-400">AI Recipe Generator</p>
                    </div>
                    
                    <!-- AI Status -->
                    <div class="ai-status connected">
                        <span class="mr-2">✅</span>
                        <span class="font-medium">OpenAI Connected</span>
                    </div>
                    
                    <!-- Recipe Generation -->
                    <div class="p-6">
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-2">Recipe Title</label>
                            <input type="text" class="glass-input" placeholder="e.g., Spicy Tuna Pasta" value="Chocolate Chip Cookies">
                        </div>
                        <button class="glass-button mb-4">
                            🤖 Generate Ingredients
                        </button>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-2">Search Viral Recipes</label>
                            <input type="text" class="glass-input" placeholder="e.g., viral desserts">
                        </div>
                        <button class="glass-button">
                            🔍 Search Viral
                        </button>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="p-6 border-t border-white/10">
                        <h4 class="text-sm font-medium mb-3">Quick Actions</h4>
                        <div class="space-y-2">
                            <button class="w-full text-left p-2 text-sm hover:bg-white/10 rounded">📚 Saved Recipes</button>
                            <button class="w-full text-left p-2 text-sm hover:bg-white/10 rounded">⚙️ Settings</button>
                            <button class="w-full text-left p-2 text-sm hover:bg-white/10 rounded">📈 Dashboard</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- AI Providers -->
            <div class="lg:col-span-1">
                <h2 class="text-2xl font-bold text-white mb-4">AI Providers</h2>
                <div class="space-y-4">
                    <div class="provider-card active">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-bold">🤖 OpenAI</h3>
                            <span class="text-green-400 text-sm">✅ Connected</span>
                        </div>
                        <p class="text-sm text-gray-300 mb-2">GPT-3.5 Turbo, GPT-4, GPT-4 Turbo</p>
                        <div class="text-xs text-gray-400">
                            <div>Model: GPT-3.5 Turbo</div>
                            <div>Temperature: 0.7</div>
                            <div>Max Tokens: 1500</div>
                        </div>
                    </div>
                    
                    <div class="provider-card">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-bold">🧠 DeepSeek</h3>
                            <span class="text-gray-400 text-sm">⚙️ Configure</span>
                        </div>
                        <p class="text-sm text-gray-300 mb-2">Cost-effective AI alternative</p>
                        <div class="text-xs text-gray-400">
                            <div>Model: DeepSeek Chat</div>
                            <div>Status: API key required</div>
                        </div>
                    </div>
                    
                    <div class="provider-card">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-bold">✨ Google Gemini</h3>
                            <span class="text-gray-400 text-sm">⚙️ Configure</span>
                        </div>
                        <p class="text-sm text-gray-300 mb-2">Fast and efficient Google AI</p>
                        <div class="text-xs text-gray-400">
                            <div>Model: Gemini Pro</div>
                            <div>Status: API key required</div>
                        </div>
                    </div>
                    
                    <div class="provider-card">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-bold">🌐 OpenRouter</h3>
                            <span class="text-gray-400 text-sm">⚙️ Configure</span>
                        </div>
                        <p class="text-sm text-gray-300 mb-2">Access to 20+ AI models</p>
                        <div class="text-xs text-gray-400">
                            <div>Model: Claude-3 Haiku</div>
                            <div>Status: API key required</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Features Overview -->
            <div class="lg:col-span-1">
                <h2 class="text-2xl font-bold text-white mb-4">Enhanced Features</h2>
                <div class="glass-card p-6">
                    <div class="space-y-4">
                        <div class="border-b border-white/10 pb-4">
                            <h3 class="font-bold text-green-400 mb-2">✅ Completed Updates</h3>
                            <ul class="text-sm space-y-1">
                                <li>• Expanded sidebar to 350px</li>
                                <li>• Multiple AI provider support</li>
                                <li>• Real-time AI status indicator</li>
                                <li>• Recent activity tracking</li>
                                <li>• Advanced settings panel</li>
                                <li>• Enhanced glassmorphism UI</li>
                                <li>• Smart API key validation</li>
                            </ul>
                        </div>
                        
                        <div class="border-b border-white/10 pb-4">
                            <h3 class="font-bold text-blue-400 mb-2">🔧 Technical Improvements</h3>
                            <ul class="text-sm space-y-1">
                                <li>• TypeScript interfaces updated</li>
                                <li>• Enhanced error handling</li>
                                <li>• Optimized component rendering</li>
                                <li>• Improved state management</li>
                                <li>• Better security practices</li>
                            </ul>
                        </div>
                        
                        <div>
                            <h3 class="font-bold text-purple-400 mb-2">🎨 UI/UX Enhancements</h3>
                            <ul class="text-sm space-y-1">
                                <li>• Smooth animations</li>
                                <li>• Hover effects</li>
                                <li>• Status color coding</li>
                                <li>• Responsive design</li>
                                <li>• Accessibility improvements</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="glass-card p-6 mt-6">
                    <h3 class="font-bold text-yellow-400 mb-3">📊 Provider Comparison</h3>
                    <div class="text-xs space-y-2">
                        <div class="flex justify-between">
                            <span>OpenAI:</span>
                            <span class="text-green-400">High Quality, $$</span>
                        </div>
                        <div class="flex justify-between">
                            <span>DeepSeek:</span>
                            <span class="text-blue-400">Good Quality, $</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Gemini:</span>
                            <span class="text-purple-400">Fast, $$</span>
                        </div>
                        <div class="flex justify-between">
                            <span>OpenRouter:</span>
                            <span class="text-orange-400">Multiple Models, $-$$$</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-8">
            <div class="glass-card p-6 inline-block">
                <h3 class="text-xl font-bold text-white mb-2">Ready to Test!</h3>
                <p class="text-white/80 mb-4">Load the extension from the dist folder to try all new features</p>
                <div class="text-sm text-white/60">
                    Chrome Extensions → Developer Mode → Load Unpacked → Select 'dist' folder
                </div>
            </div>
        </div>
    </div>
</body>
</html>
