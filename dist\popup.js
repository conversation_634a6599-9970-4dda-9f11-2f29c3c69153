// ViralChef Enhanced Popup Script
document.addEventListener('DOMContentLoaded', function() {
  // Main elements
  const generateBtn = document.getElementById('generate-btn');
  const searchBtn = document.getElementById('search-btn');
  const recipeTitleInput = document.getElementById('recipe-title');
  const searchQueryInput = document.getElementById('search-query');
  const loading = document.getElementById('loading');
  const status = document.getElementById('status');
  const closeBtn = document.getElementById('closeBtn');
  const overlay = document.getElementById('overlay');

  // AI Status elements
  const aiStatus = document.getElementById('ai-status');
  const aiModel = document.getElementById('ai-model');
  const aiLastUsed = document.getElementById('ai-last-used');

  // Tab elements
  const navTabs = document.querySelectorAll('.nav-tab');
  const tabContents = document.querySelectorAll('.tab-content');

  // Settings elements
  const providerOptions = document.querySelectorAll('.provider-option');
  const apiKeyInput = document.getElementById('api-key');
  const modelSelect = document.getElementById('model-select');
  const temperatureSlider = document.getElementById('temperature');
  const tempValue = document.getElementById('temp-value');
  const saveSettingsBtn = document.getElementById('save-settings');

  // Quick action buttons
  const dashboardAction = document.getElementById('dashboard-action');
  const savedRecipesBtn = document.getElementById('saved-recipes');
  const trendingBtn = document.getElementById('trending');
  const favoritesBtn = document.getElementById('favorites');

  // Current settings
  let currentSettings = {
    aiProvider: 'openai',
    apiKey: '',
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 1500
  };

  // Initialize popup
  initializePopup();

  // Tab Navigation
  navTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const targetTab = tab.dataset.tab;
      switchTab(targetTab);
    });
  });

  function switchTab(tabName) {
    // Update nav tabs
    navTabs.forEach(tab => {
      tab.classList.toggle('active', tab.dataset.tab === tabName);
    });

    // Update tab content
    tabContents.forEach(content => {
      content.classList.toggle('active', content.id === `${tabName}-tab`);
    });
  }

  // Provider Selection
  providerOptions.forEach(option => {
    option.addEventListener('click', () => {
      const provider = option.dataset.provider;
      selectProvider(provider);
    });
  });

  function selectProvider(provider) {
    currentSettings.aiProvider = provider;

    // Update UI
    providerOptions.forEach(option => {
      option.classList.toggle('active', option.dataset.provider === provider);
    });

    // Update model options
    updateModelOptions(provider);

    // Load saved API key for this provider
    loadApiKey(provider);
  }

  function updateModelOptions(provider) {
    const models = getAvailableModels(provider);
    modelSelect.innerHTML = '';

    models.forEach(model => {
      const option = document.createElement('option');
      option.value = model;
      option.textContent = model;
      modelSelect.appendChild(option);
    });

    // Set default model
    const defaultModel = models[0];
    modelSelect.value = defaultModel;
    currentSettings.model = defaultModel;
  }

  function getAvailableModels(provider) {
    const modelMap = {
      openai: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'gpt-4o', 'gpt-4o-mini'],
      deepseek: ['deepseek-chat', 'deepseek-coder'],
      gemini: ['gemini-pro', 'gemini-1.5-pro', 'gemini-1.5-flash'],
      openrouter: ['anthropic/claude-3-haiku', 'anthropic/claude-3-sonnet', 'openai/gpt-3.5-turbo', 'openai/gpt-4', 'google/gemini-pro']
    };

    return modelMap[provider] || ['gpt-3.5-turbo'];
  }

  // Temperature Slider
  temperatureSlider.addEventListener('input', (e) => {
    const value = parseFloat(e.target.value);
    tempValue.textContent = value;
    currentSettings.temperature = value;
  });

  // Model Selection
  modelSelect.addEventListener('change', (e) => {
    currentSettings.model = e.target.value;
  });

  // API Key Input
  apiKeyInput.addEventListener('input', (e) => {
    currentSettings.apiKey = e.target.value;
  });

  // Save Settings
  saveSettingsBtn.addEventListener('click', saveSettings);

  async function saveSettings() {
    try {
      // Validate API key
      if (!currentSettings.apiKey) {
        showStatus('Please enter an API key', 'error');
        return;
      }

      // Save to storage
      const settingsToSave = {
        aiProvider: currentSettings.aiProvider,
        [`${currentSettings.aiProvider}Key`]: currentSettings.apiKey,
        [`${currentSettings.aiProvider}Model`]: currentSettings.model,
        temperature: currentSettings.temperature,
        maxTokens: currentSettings.maxTokens
      };

      await chrome.storage.sync.set(settingsToSave);

      showStatus('Settings saved successfully!', 'success');

      // Update AI status
      checkAIStatus();

    } catch (error) {
      console.error('Failed to save settings:', error);
      showStatus('Failed to save settings', 'error');
    }
  }

  // Close functionality
  function closeSidebar() {
    const container = document.querySelector('.container');
    const overlay = document.querySelector('.sidebar-overlay');

    container.style.animation = 'slideOutToLeft 0.4s ease-in forwards';
    overlay.style.animation = 'fadeOut 0.4s ease-in forwards';

    setTimeout(() => {
      window.close();
    }, 400);
  }

  // Add close animations
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideOutToLeft {
      0% { transform: translateX(0); opacity: 1; }
      100% { transform: translateX(-100%); opacity: 0; }
    }
    @keyframes fadeOut {
      0% { opacity: 1; }
      100% { opacity: 0; }
    }
  `;
  document.head.appendChild(style);

  // Close button event
  closeBtn.addEventListener('click', closeSidebar);

  // Close on overlay click
  overlay.addEventListener('click', closeSidebar);

  // AI Status Management
  async function checkAIStatus() {
    try {
      updateAIStatus('loading', 'Checking AI Status...', 'Loading...', '--');

      const response = await sendMessage({ action: 'checkAIStatus' });

      if (response.success && response.status.connected) {
        updateAIStatus(
          'connected',
          `${response.status.provider.toUpperCase()} Connected`,
          response.status.model,
          response.status.lastUsed
        );
      } else {
        updateAIStatus(
          'error',
          'AI Not Connected',
          response.status?.error || 'API key required',
          '--'
        );
      }
    } catch (error) {
      updateAIStatus('error', 'Connection Error', error.message, '--');
    }
  }

  function updateAIStatus(state, text, model, lastUsed) {
    if (!aiStatus) return;

    // Remove all status classes
    aiStatus.classList.remove('connected', 'error', 'loading');

    // Add new status class
    aiStatus.classList.add(state);

    // Update icon
    const icon = aiStatus.querySelector('.ai-status-icon');
    if (icon) {
      switch (state) {
        case 'connected':
          icon.textContent = '✅';
          break;
        case 'error':
          icon.textContent = '❌';
          break;
        case 'loading':
          icon.textContent = '⏳';
          break;
      }
    }

    // Update text
    const textEl = aiStatus.querySelector('.ai-status-text');
    if (textEl) {
      textEl.textContent = text;
    }

    // Update model
    if (aiModel) {
      aiModel.textContent = model;
    }

    // Update last used
    if (aiLastUsed) {
      aiLastUsed.textContent = lastUsed;
    }
  }

  // Load settings from storage
  async function loadSettings() {
    try {
      const settings = await chrome.storage.sync.get([
        'aiProvider', 'openaiKey', 'deepseekKey', 'geminiKey', 'openrouterKey',
        'openaiModel', 'deepseekModel', 'geminiModel', 'openrouterModel',
        'temperature', 'maxTokens'
      ]);

      // Update current settings
      currentSettings.aiProvider = settings.aiProvider || 'openai';
      currentSettings.temperature = settings.temperature || 0.7;
      currentSettings.maxTokens = settings.maxTokens || 1500;

      // Update UI
      selectProvider(currentSettings.aiProvider);
      temperatureSlider.value = currentSettings.temperature;
      tempValue.textContent = currentSettings.temperature;

      // Load API key for current provider
      loadApiKey(currentSettings.aiProvider);

    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  async function loadApiKey(provider) {
    try {
      const result = await chrome.storage.sync.get([`${provider}Key`]);
      const apiKey = result[`${provider}Key`] || '';

      currentSettings.apiKey = apiKey;
      apiKeyInput.value = apiKey;

    } catch (error) {
      console.error('Failed to load API key:', error);
    }
  }

  // Initialize popup
  async function initializePopup() {
    await loadSettings();
    await checkAIStatus();
  }

  // Close on Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeSidebar();
    }
  });
  
  // Generate recipe ingredients
  generateBtn.addEventListener('click', async () => {
    const title = recipeTitleInput.value.trim();
    if (!title) {
      showStatus('Please enter a recipe title', 'error');
      return;
    }
    
    showLoading(true);
    showStatus('Generating ingredients with AI...');
    
    try {
      const response = await sendMessage({
        action: 'generateRecipe',
        data: { title }
      });
      
      if (response.success) {
        showStatus('Recipe generated successfully!', 'success');
        // Open dashboard to show results
        setTimeout(() => {
          chrome.runtime.sendMessage({ action: 'openDashboard' });
          window.close();
        }, 1000);
      } else {
        showStatus('Failed to generate recipe: ' + response.error, 'error');
      }
    } catch (error) {
      showStatus('Error: ' + error.message, 'error');
    } finally {
      showLoading(false);
    }
  });
  
  // Search viral recipes
  searchBtn.addEventListener('click', async () => {
    const query = searchQueryInput.value.trim();
    if (!query) {
      showStatus('Please enter a search query', 'error');
      return;
    }
    
    showLoading(true);
    showStatus('Searching for viral recipes...');
    
    try {
      const response = await sendMessage({
        action: 'searchViralRecipes',
        data: { query }
      });
      
      if (response.success) {
        showStatus(`Found ${response.results.length} viral recipes!`, 'success');
        // Open dashboard to show results
        setTimeout(() => {
          chrome.runtime.sendMessage({ action: 'openDashboard' });
          window.close();
        }, 1000);
      } else {
        showStatus('Search failed: ' + response.error, 'error');
      }
    } catch (error) {
      showStatus('Error: ' + error.message, 'error');
    } finally {
      showLoading(false);
    }
  });
  
  // Quick actions
  dashboardAction.addEventListener('click', () => {
    chrome.runtime.sendMessage({ action: 'openDashboard' });
    window.close();
  });

  savedRecipesBtn.addEventListener('click', () => {
    chrome.runtime.sendMessage({
      action: 'openDashboard',
      data: { tab: 'saved' }
    });
    window.close();
  });

  trendingBtn.addEventListener('click', () => {
    chrome.runtime.sendMessage({
      action: 'openDashboard',
      data: { tab: 'trending' }
    });
    window.close();
  });
  
  favoritesBtn.addEventListener('click', () => {
    chrome.runtime.sendMessage({ 
      action: 'openDashboard',
      data: { tab: 'favorites' }
    });
    window.close();
  });
  
  // Enter key handlers
  recipeTitleInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      generateBtn.click();
    }
  });
  
  searchQueryInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      searchBtn.click();
    }
  });
  
  // Helper functions
  function showLoading(show) {
    loading.style.display = show ? 'block' : 'none';
    generateBtn.disabled = show;
    searchBtn.disabled = show;
    saveSettingsBtn.disabled = show;
  }

  function showStatus(message, type = 'info') {
    if (!status) return;

    status.textContent = message;
    status.className = `status ${type}`;

    // Clear status after 3 seconds for success/error messages
    if (type !== 'info') {
      setTimeout(() => {
        status.textContent = 'Ready to generate amazing recipes!';
        status.className = 'status';
      }, 3000);
    }
  }

  function sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
  }

  // Auto-save settings on change
  function autoSaveSettings() {
    clearTimeout(autoSaveSettings.timeout);
    autoSaveSettings.timeout = setTimeout(async () => {
      try {
        const settingsToSave = {
          temperature: currentSettings.temperature,
          maxTokens: currentSettings.maxTokens
        };

        await chrome.storage.sync.set(settingsToSave);
      } catch (error) {
        console.error('Auto-save failed:', error);
      }
    }, 1000);
  }

  // Enhanced placeholder suggestions
  async function loadPopupData() {
    try {
      // Get recent activity
      const data = await chrome.storage.local.get(['recentActivity', 'dailyStats']);

      // Update placeholders with trending suggestions
      const suggestions = [
        'Viral TikTok Pasta',
        'Trending Chocolate Cake',
        'Popular Korean Corn Dogs',
        'Viral Baked Feta Pasta',
        'Trending Cloud Bread',
        'Popular Dalgona Coffee'
      ];

      const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
      recipeTitleInput.placeholder = `e.g., ${randomSuggestion}`;

      // Update search placeholder
      const searchSuggestions = [
        'viral desserts 2024',
        'trending pasta recipes',
        'popular TikTok food',
        'viral baking trends'
      ];

      const randomSearchSuggestion = searchSuggestions[Math.floor(Math.random() * searchSuggestions.length)];
      searchQueryInput.placeholder = `e.g., ${randomSearchSuggestion}`;

      // Show daily stats if available
      if (data.dailyStats) {
        const today = new Date().toDateString();
        const todayStats = data.dailyStats[today];

        if (todayStats) {
          const total = todayStats.generated + todayStats.searched;
          if (total > 0) {
            showStatus(`Today: ${todayStats.generated} recipes generated, ${todayStats.searched} searches`);
          }
        }
      }

    } catch (error) {
      console.error('Failed to load popup data:', error);
      showStatus('Welcome to ViralChef! 🍳');
    }
  }

  // Keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + Enter to generate recipe
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      if (document.activeElement === recipeTitleInput) {
        generateBtn.click();
      } else if (document.activeElement === searchQueryInput) {
        searchBtn.click();
      }
    }

    // Tab navigation
    if (e.key === 'Tab' && e.shiftKey) {
      // Shift+Tab for previous tab
      e.preventDefault();
      const currentTab = document.querySelector('.nav-tab.active');
      const tabs = Array.from(navTabs);
      const currentIndex = tabs.indexOf(currentTab);
      const prevIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1;
      tabs[prevIndex].click();
    } else if (e.key === 'Tab' && !e.shiftKey && e.target.classList.contains('nav-tab')) {
      // Tab for next tab
      e.preventDefault();
      const currentTab = document.querySelector('.nav-tab.active');
      const tabs = Array.from(navTabs);
      const currentIndex = tabs.indexOf(currentTab);
      const nextIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0;
      tabs[nextIndex].click();
    }
  });

  // Auto-save temperature changes
  temperatureSlider.addEventListener('input', autoSaveSettings);

  console.log('ViralChef Enhanced Popup loaded successfully! 🍳✨');
});
