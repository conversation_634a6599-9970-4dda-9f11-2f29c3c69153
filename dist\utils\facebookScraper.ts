// Facebook Scraper Utility for Viral Recipe Discovery
export interface FacebookPost {
  id: string
  content: string
  author: string
  groupName: string
  url: string
  engagement: {
    likes: number
    comments: number
    shares: number
  }
  timestamp: string
  images?: string[]
  recipeTitle?: string
  confidence: number
}

export interface FacebookGroup {
  id: string
  name: string
  memberCount: number
  category: string
  isPublic: boolean
}

export interface SearchOptions {
  keywords: string[]
  groups?: string[]
  dateRange?: {
    start: Date
    end: Date
  }
  minEngagement?: number
  maxResults?: number
}

// Facebook Graph API integration (requires proper permissions)
export class FacebookGraphAPI {
  private accessToken: string
  private baseUrl = 'https://graph.facebook.com/v18.0'

  constructor(accessToken: string) {
    this.accessToken = accessToken
  }

  async searchGroups(query: string): Promise<FacebookGroup[]> {
    try {
      const response = await fetch(
        `${this.baseUrl}/search?q=${encodeURIComponent(query)}&type=group&access_token=${this.accessToken}`
      )
      
      if (!response.ok) {
        throw new Error(`Facebook API error: ${response.status}`)
      }

      const data = await response.json()
      
      return data.data.map((group: any) => ({
        id: group.id,
        name: group.name,
        memberCount: group.member_count || 0,
        category: group.category || 'Unknown',
        isPublic: group.privacy === 'OPEN'
      }))
    } catch (error) {
      console.error('Facebook group search failed:', error)
      return []
    }
  }

  async getGroupPosts(groupId: string, options: SearchOptions): Promise<FacebookPost[]> {
    try {
      const fields = 'id,message,from,created_time,likes.summary(true),comments.summary(true),shares'
      const response = await fetch(
        `${this.baseUrl}/${groupId}/posts?fields=${fields}&access_token=${this.accessToken}&limit=${options.maxResults || 50}`
      )
      
      if (!response.ok) {
        throw new Error(`Facebook API error: ${response.status}`)
      }

      const data = await response.json()
      
      return data.data
        .filter((post: any) => this.isRecipePost(post.message, options.keywords))
        .map((post: any) => this.parsePost(post))
        .sort((a: FacebookPost, b: FacebookPost) => b.engagement.likes - a.engagement.likes)
    } catch (error) {
      console.error('Facebook posts fetch failed:', error)
      return []
    }
  }

  private isRecipePost(content: string, keywords: string[]): boolean {
    if (!content) return false
    
    const lowerContent = content.toLowerCase()
    const recipeKeywords = [
      'recipe', 'cook', 'bake', 'ingredient', 'dish', 'meal',
      'delicious', 'tasty', 'homemade', 'kitchen', 'food',
      ...keywords.map(k => k.toLowerCase())
    ]
    
    return recipeKeywords.some(keyword => lowerContent.includes(keyword))
  }

  private parsePost(post: any): FacebookPost {
    const engagement = {
      likes: post.likes?.summary?.total_count || 0,
      comments: post.comments?.summary?.total_count || 0,
      shares: post.shares?.count || 0
    }

    const totalEngagement = engagement.likes + engagement.comments + engagement.shares
    const confidence = this.calculateRecipeConfidence(post.message, totalEngagement)

    return {
      id: post.id,
      content: post.message || '',
      author: post.from?.name || 'Unknown',
      groupName: 'Facebook Group',
      url: `https://facebook.com/${post.id}`,
      engagement,
      timestamp: post.created_time,
      recipeTitle: this.extractRecipeTitle(post.message),
      confidence
    }
  }

  private calculateRecipeConfidence(content: string, engagement: number): number {
    let score = 0
    
    if (!content) return 0
    
    const lowerContent = content.toLowerCase()
    
    // Content-based scoring
    if (lowerContent.includes('recipe')) score += 30
    if (lowerContent.includes('ingredients')) score += 25
    if (lowerContent.includes('instructions') || lowerContent.includes('steps')) score += 20
    if (lowerContent.includes('delicious') || lowerContent.includes('amazing')) score += 10
    if (lowerContent.includes('homemade')) score += 15
    
    // Engagement-based scoring
    if (engagement > 100) score += 20
    if (engagement > 500) score += 30
    if (engagement > 1000) score += 40
    
    return Math.min(100, score)
  }

  private extractRecipeTitle(content: string): string | undefined {
    if (!content) return undefined
    
    // Try to extract recipe title from common patterns
    const patterns = [
      /(?:recipe for|making|how to make)\s+([^.!?\n]+)/i,
      /^([^.!?\n]+(?:recipe|dish|meal))/i,
      /([A-Z][^.!?\n]{10,50}(?:recipe|pasta|chicken|beef|cake|bread))/i
    ]
    
    for (const pattern of patterns) {
      const match = content.match(pattern)
      if (match && match[1]) {
        return match[1].trim()
      }
    }
    
    // Fallback: use first sentence if it looks like a title
    const firstSentence = content.split(/[.!?\n]/)[0].trim()
    if (firstSentence.length > 10 && firstSentence.length < 100) {
      return firstSentence
    }
    
    return undefined
  }
}

// Alternative scraping approach (for public content)
export class FacebookScraper {

  async searchPublicPosts(options: SearchOptions): Promise<FacebookPost[]> {
    // Note: This is a simplified implementation
    // In practice, you'd need to handle Facebook's anti-scraping measures

    try {
      // Search for public posts using Facebook's search URL
      const searchQuery = options.keywords.join(' ')
      const searchUrl = `https://www.facebook.com/search/posts/?q=${encodeURIComponent(searchQuery)}`

      // This would require a proper scraping setup with headless browser
      console.log('Would scrape:', searchUrl)

      // Return mock data for now
      return this.getMockResults(options)
    } catch (error) {
      console.error('Facebook scraping failed:', error)
      return []
    }
  }

  private getMockResults(_options: SearchOptions): FacebookPost[] {
    // Mock data for development/testing
    return [
      {
        id: 'mock_1',
        content: 'Amazing homemade pasta recipe! This creamy garlic pasta is so delicious and easy to make. Perfect for weeknight dinners!',
        author: 'Chef Maria',
        groupName: 'Home Cooking Enthusiasts',
        url: 'https://facebook.com/mock_1',
        engagement: {
          likes: 1250,
          comments: 89,
          shares: 156
        },
        timestamp: new Date().toISOString(),
        recipeTitle: 'Creamy Garlic Pasta',
        confidence: 85
      },
      {
        id: 'mock_2',
        content: 'Quick and easy chicken stir-fry recipe that my family loves! Ready in 20 minutes with simple ingredients.',
        author: 'Cooking Mom',
        groupName: 'Quick Family Meals',
        url: 'https://facebook.com/mock_2',
        engagement: {
          likes: 890,
          comments: 45,
          shares: 78
        },
        timestamp: new Date(Date.now() - 86400000).toISOString(),
        recipeTitle: 'Quick Chicken Stir-Fry',
        confidence: 78
      }
    ]
  }
}

// Main Facebook search service
export class FacebookRecipeSearch {
  private graphAPI?: FacebookGraphAPI
  private scraper: FacebookScraper

  constructor(accessToken?: string) {
    if (accessToken) {
      this.graphAPI = new FacebookGraphAPI(accessToken)
    }
    this.scraper = new FacebookScraper()
  }

  async searchViralRecipes(options: SearchOptions): Promise<FacebookPost[]> {
    const results: FacebookPost[] = []

    try {
      // Try Graph API first if available
      if (this.graphAPI && options.groups) {
        for (const groupId of options.groups) {
          const posts = await this.graphAPI.getGroupPosts(groupId, options)
          results.push(...posts)
        }
      }

      // Fallback to public scraping
      if (results.length === 0) {
        const scrapedPosts = await this.scraper.searchPublicPosts(options)
        results.push(...scrapedPosts)
      }

      // Filter and sort results
      return this.filterAndSortResults(results, options)
    } catch (error) {
      console.error('Facebook recipe search failed:', error)
      return []
    }
  }

  private filterAndSortResults(posts: FacebookPost[], options: SearchOptions): FacebookPost[] {
    let filtered = posts

    // Filter by engagement threshold
    if (options.minEngagement) {
      filtered = filtered.filter(post => {
        const totalEngagement = post.engagement.likes + post.engagement.comments + post.engagement.shares
        return totalEngagement >= (options.minEngagement || 0)
      })
    }

    // Filter by date range
    if (options.dateRange) {
      filtered = filtered.filter(post => {
        const postDate = new Date(post.timestamp)
        return postDate >= options.dateRange!.start && postDate <= options.dateRange!.end
      })
    }

    // Sort by confidence and engagement
    filtered.sort((a, b) => {
      const aScore = a.confidence + (a.engagement.likes / 10)
      const bScore = b.confidence + (b.engagement.likes / 10)
      return bScore - aScore
    })

    // Limit results
    return filtered.slice(0, options.maxResults || 20)
  }

  async getPopularGroups(category: string = 'cooking'): Promise<FacebookGroup[]> {
    if (!this.graphAPI) {
      // Return mock popular groups
      return [
        {
          id: 'cooking_group_1',
          name: 'Home Cooking Enthusiasts',
          memberCount: 125000,
          category: 'Cooking',
          isPublic: true
        },
        {
          id: 'recipe_group_1',
          name: 'Quick & Easy Recipes',
          memberCount: 89000,
          category: 'Food & Cooking',
          isPublic: true
        }
      ]
    }

    return await this.graphAPI.searchGroups(category)
  }
}

// Utility functions
export async function getFacebookAccessToken(): Promise<string | null> {
  const settings = await chrome.storage.sync.get(['facebookAccessToken'])
  return settings.facebookAccessToken || null
}

export async function searchFacebookRecipes(keywords: string[], options?: Partial<SearchOptions>): Promise<FacebookPost[]> {
  const accessToken = await getFacebookAccessToken()
  const searchService = new FacebookRecipeSearch(accessToken || undefined)
  
  const searchOptions: SearchOptions = {
    keywords,
    maxResults: 20,
    minEngagement: 50,
    ...options
  }
  
  return await searchService.searchViralRecipes(searchOptions)
}

export function isValidFacebookUrl(url: string): boolean {
  return /^https?:\/\/(www\.)?facebook\.com\//.test(url)
}

export function extractFacebookPostId(url: string): string | null {
  const match = url.match(/facebook\.com\/.*\/posts\/(\d+)/)
  return match ? match[1] : null
}
