import React from 'react'
import { LucideIcon } from 'lucide-react'

interface GlassButtonProps {
  children: React.ReactNode
  onClick?: () => void
  disabled?: boolean
  loading?: boolean
  variant?: 'primary' | 'secondary' | 'success' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  icon?: LucideIcon
  className?: string
  type?: 'button' | 'submit' | 'reset'
}

const GlassButton: React.FC<GlassButtonProps> = ({
  children,
  onClick,
  disabled = false,
  loading = false,
  variant = 'primary',
  size = 'md',
  icon: Icon,
  className = '',
  type = 'button'
}) => {
  const baseClasses = 'relative inline-flex items-center justify-center font-medium rounded-xl transition-all duration-300 backdrop-blur-lg border focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'
  
  const variantClasses = {
    primary: 'bg-gradient-to-r from-emerald-400/80 to-blue-400/80 hover:from-emerald-500/90 hover:to-blue-500/90 text-white border-white/20 hover:border-white/30 focus:ring-emerald-500 shadow-lg hover:shadow-xl',
    secondary: 'bg-gradient-to-r from-gray-400/60 to-gray-500/60 hover:from-gray-500/70 hover:to-gray-600/70 text-white border-white/20 hover:border-white/30 focus:ring-gray-500 shadow-md hover:shadow-lg',
    success: 'bg-gradient-to-r from-green-400/80 to-emerald-500/80 hover:from-green-500/90 hover:to-emerald-600/90 text-white border-white/20 hover:border-white/30 focus:ring-green-500 shadow-lg hover:shadow-xl',
    danger: 'bg-gradient-to-r from-red-400/80 to-pink-500/80 hover:from-red-500/90 hover:to-pink-600/90 text-white border-white/20 hover:border-white/30 focus:ring-red-500 shadow-lg hover:shadow-xl'
  }
  
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm space-x-1',
    md: 'px-6 py-3 text-base space-x-2',
    lg: 'px-8 py-4 text-lg space-x-3'
  }
  
  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick()
    }
  }

  return (
    <button
      type={type}
      onClick={handleClick}
      disabled={disabled || loading}
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${disabled || loading ? 'transform-none' : 'hover:scale-105 active:scale-95'}
        ${className}
      `}
    >
      {/* Loading spinner */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
        </div>
      )}
      
      {/* Content */}
      <div className={`flex items-center ${loading ? 'opacity-0' : 'opacity-100'}`}>
        {Icon && (
          <Icon className={`${iconSizes[size]} ${children ? 'mr-2' : ''}`} />
        )}
        {children}
      </div>
      
      {/* Glass shimmer effect */}
      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500 animate-shimmer"></div>
    </button>
  )
}

export default GlassButton
