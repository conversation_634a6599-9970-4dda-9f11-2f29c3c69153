// AI Integration Utilities for Recipe Generation
export interface RecipeIngredient {
  name: string
  amount: string
  unit: string
  notes?: string
}

export interface GeneratedRecipe {
  title: string
  ingredients: RecipeIngredient[]
  servings: number
  prepTime: string
  difficulty: 'Easy' | 'Medium' | 'Hard'
  cuisine: string
  dietaryInfo: string[]
  tips: string[]
  nutritionNotes?: string
}

export interface AIProvider {
  name: 'openai' | 'deepseek' | 'gemini' | 'openrouter'
  apiKey: string
  model: string
  baseUrl?: string
}

// OpenAI API integration
export class OpenAIService {
  private apiKey: string
  private model: string
  private baseUrl = 'https://api.openai.com/v1'

  constructor(apiKey: string, model: string = 'gpt-3.5-turbo') {
    this.apiKey = apiKey
    this.model = model
  }

  async generateRecipe(title: string, preferences?: any): Promise<GeneratedRecipe> {
    const prompt = this.buildRecipePrompt(title, preferences)
    
    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            {
              role: 'system',
              content: 'You are a professional chef and recipe developer. Generate detailed, accurate recipes with precise measurements and helpful tips.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 1500
        })
      })

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`)
      }

      const data = await response.json()
      return this.parseRecipeResponse(data.choices[0].message.content, title)
    } catch (error) {
      console.error('OpenAI recipe generation failed:', error)
      throw new Error('Failed to generate recipe with OpenAI')
    }
  }

  public buildRecipePrompt(title: string, preferences?: any): string {
    let prompt = `Generate a complete recipe for "${title}". Please provide:

1. A list of ingredients with precise measurements
2. Estimated serving size
3. Preparation time
4. Difficulty level (Easy/Medium/Hard)
5. Cuisine type
6. Dietary information (vegetarian, gluten-free, etc.)
7. Helpful cooking tips
8. Basic nutritional notes

Format the response as JSON with the following structure:
{
  "ingredients": [{"name": "ingredient", "amount": "1", "unit": "cup", "notes": "optional"}],
  "servings": 4,
  "prepTime": "30 minutes",
  "difficulty": "Easy",
  "cuisine": "Italian",
  "dietaryInfo": ["vegetarian"],
  "tips": ["tip1", "tip2"],
  "nutritionNotes": "High in protein"
}`

    if (preferences) {
      if (preferences.dietary) {
        prompt += `\n\nDietary restrictions: ${preferences.dietary.join(', ')}`
      }
      if (preferences.servings) {
        prompt += `\nServing size: ${preferences.servings} people`
      }
      if (preferences.difficulty) {
        prompt += `\nPreferred difficulty: ${preferences.difficulty}`
      }
    }

    return prompt
  }

  public parseRecipeResponse(response: string, title: string): GeneratedRecipe {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        return {
          title,
          ingredients: parsed.ingredients || [],
          servings: parsed.servings || 4,
          prepTime: parsed.prepTime || '30 minutes',
          difficulty: parsed.difficulty || 'Medium',
          cuisine: parsed.cuisine || 'International',
          dietaryInfo: parsed.dietaryInfo || [],
          tips: parsed.tips || [],
          nutritionNotes: parsed.nutritionNotes
        }
      }
    } catch (error) {
      console.error('Failed to parse recipe JSON:', error)
    }

    // Fallback: parse text response
    return this.parseTextResponse(response, title)
  }

  private parseTextResponse(response: string, title: string): GeneratedRecipe {
    // Basic text parsing fallback
    const lines = response.split('\n').filter(line => line.trim())
    const ingredients: RecipeIngredient[] = []
    
    // Extract ingredients (simple pattern matching)
    let inIngredients = false
    for (const line of lines) {
      if (line.toLowerCase().includes('ingredient')) {
        inIngredients = true
        continue
      }
      if (inIngredients && line.match(/^\d+|^-|^\*/)) {
        const match = line.match(/^[\d\-\*\s]*(.+)/)
        if (match) {
          ingredients.push({
            name: match[1].trim(),
            amount: '1',
            unit: 'unit'
          })
        }
      }
      if (line.toLowerCase().includes('instruction') || line.toLowerCase().includes('step')) {
        break
      }
    }

    return {
      title,
      ingredients: ingredients.length > 0 ? ingredients : [
        { name: 'Main ingredient', amount: '1', unit: 'unit' },
        { name: 'Seasoning', amount: 'to taste', unit: '' }
      ],
      servings: 4,
      prepTime: '30 minutes',
      difficulty: 'Medium',
      cuisine: 'International',
      dietaryInfo: [],
      tips: ['Follow the recipe carefully', 'Adjust seasoning to taste']
    }
  }
}

// DeepSeek API integration
export class DeepSeekService {
  private apiKey: string
  private model: string
  private baseUrl = 'https://api.deepseek.com/v1'

  constructor(apiKey: string, model: string = 'deepseek-chat') {
    this.apiKey = apiKey
    this.model = model
  }

  async generateRecipe(title: string, preferences?: any): Promise<GeneratedRecipe> {
    // Similar implementation to OpenAI but with DeepSeek API
    const prompt = this.buildRecipePrompt(title, preferences)

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            {
              role: 'system',
              content: 'You are a professional chef and recipe developer. Generate detailed, accurate recipes with precise measurements and helpful tips.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 1500
        })
      })

      if (!response.ok) {
        throw new Error(`DeepSeek API error: ${response.status}`)
      }

      const data = await response.json()
      return this.parseRecipeResponse(data.choices[0].message.content, title)
    } catch (error) {
      console.error('DeepSeek recipe generation failed:', error)
      throw new Error('Failed to generate recipe with DeepSeek')
    }
  }

  private buildRecipePrompt(title: string, preferences?: any): string {
    // Same as OpenAI implementation
    return new OpenAIService('', '').buildRecipePrompt(title, preferences)
  }

  private parseRecipeResponse(response: string, title: string): GeneratedRecipe {
    // Same parsing logic as OpenAI
    return new OpenAIService('', '').parseRecipeResponse(response, title)
  }
}

// Gemini API integration
export class GeminiService {
  private apiKey: string
  private model: string
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta'

  constructor(apiKey: string, model: string = 'gemini-pro') {
    this.apiKey = apiKey
    this.model = model
  }

  async generateRecipe(title: string, preferences?: any): Promise<GeneratedRecipe> {
    const prompt = this.buildRecipePrompt(title, preferences)

    try {
      const response = await fetch(`${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: `You are a professional chef and recipe developer. Generate detailed, accurate recipes with precise measurements and helpful tips.\n\n${prompt}`
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            maxOutputTokens: 1500
          }
        })
      })

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status}`)
      }

      const data = await response.json()
      const content = data.candidates?.[0]?.content?.parts?.[0]?.text
      if (!content) {
        throw new Error('No content received from Gemini')
      }

      return this.parseRecipeResponse(content, title)
    } catch (error) {
      console.error('Gemini recipe generation failed:', error)
      throw new Error('Failed to generate recipe with Gemini')
    }
  }

  private buildRecipePrompt(title: string, preferences?: any): string {
    // Same as OpenAI implementation
    return new OpenAIService('', '').buildRecipePrompt(title, preferences)
  }

  private parseRecipeResponse(response: string, title: string): GeneratedRecipe {
    // Same parsing logic as OpenAI
    return new OpenAIService('', '').parseRecipeResponse(response, title)
  }
}

// OpenRouter API integration (supports multiple models)
export class OpenRouterService {
  private apiKey: string
  private model: string
  private baseUrl = 'https://openrouter.ai/api/v1'

  constructor(apiKey: string, model: string = 'anthropic/claude-3-haiku') {
    this.apiKey = apiKey
    this.model = model
  }

  async generateRecipe(title: string, preferences?: any): Promise<GeneratedRecipe> {
    const prompt = this.buildRecipePrompt(title, preferences)

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://viralchef.extension',
          'X-Title': 'ViralChef Recipe Generator'
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            {
              role: 'system',
              content: 'You are a professional chef and recipe developer. Generate detailed, accurate recipes with precise measurements and helpful tips.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 1500
        })
      })

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status}`)
      }

      const data = await response.json()
      return this.parseRecipeResponse(data.choices[0].message.content, title)
    } catch (error) {
      console.error('OpenRouter recipe generation failed:', error)
      throw new Error('Failed to generate recipe with OpenRouter')
    }
  }

  private buildRecipePrompt(title: string, preferences?: any): string {
    // Same as OpenAI implementation
    return new OpenAIService('', '').buildRecipePrompt(title, preferences)
  }

  private parseRecipeResponse(response: string, title: string): GeneratedRecipe {
    // Same parsing logic as OpenAI
    return new OpenAIService('', '').parseRecipeResponse(response, title)
  }
}

// Main AI service factory
export class AIRecipeService {
  private service: OpenAIService | DeepSeekService | GeminiService | OpenRouterService

  constructor(provider: AIProvider) {
    switch (provider.name) {
      case 'openai':
        this.service = new OpenAIService(provider.apiKey, provider.model)
        break
      case 'deepseek':
        this.service = new DeepSeekService(provider.apiKey, provider.model)
        break
      case 'gemini':
        this.service = new GeminiService(provider.apiKey, provider.model)
        break
      case 'openrouter':
        this.service = new OpenRouterService(provider.apiKey, provider.model)
        break
      default:
        throw new Error(`Unsupported AI provider: ${provider.name}`)
    }
  }

  async generateRecipe(title: string, preferences?: any): Promise<GeneratedRecipe> {
    if (!title || title.trim().length === 0) {
      throw new Error('Recipe title is required')
    }

    // Add some basic validation and enhancement
    const enhancedTitle = this.enhanceTitle(title)
    
    try {
      const recipe = await this.service.generateRecipe(enhancedTitle, preferences)
      
      // Post-process the recipe
      return this.enhanceRecipe(recipe)
    } catch (error) {
      console.error('Recipe generation failed:', error)
      
      // Return a fallback recipe
      return this.getFallbackRecipe(title)
    }
  }

  private enhanceTitle(title: string): string {
    // Clean and enhance the title
    return title.trim()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s-]/g, '')
  }

  private enhanceRecipe(recipe: GeneratedRecipe): GeneratedRecipe {
    // Add any post-processing enhancements
    return {
      ...recipe,
      ingredients: recipe.ingredients.map(ingredient => ({
        ...ingredient,
        name: ingredient.name.trim(),
        amount: ingredient.amount.trim(),
        unit: ingredient.unit.trim()
      }))
    }
  }

  private getFallbackRecipe(title: string): GeneratedRecipe {
    return {
      title,
      ingredients: [
        { name: 'Main ingredient (please specify)', amount: '1', unit: 'portion' },
        { name: 'Salt', amount: '1', unit: 'pinch' },
        { name: 'Pepper', amount: '1', unit: 'pinch' }
      ],
      servings: 2,
      prepTime: '15 minutes',
      difficulty: 'Easy',
      cuisine: 'International',
      dietaryInfo: [],
      tips: ['This is a basic template. Please regenerate with a more specific title.'],
      nutritionNotes: 'Nutritional information varies by ingredients used'
    }
  }
}

// Utility functions
export async function getAIProvider(): Promise<AIProvider> {
  const settings = await chrome.storage.sync.get([
    'aiProvider', 'openaiKey', 'deepseekKey', 'geminiKey', 'openrouterKey',
    'openaiModel', 'deepseekModel', 'geminiModel', 'openrouterModel'
  ])

  const provider = settings.aiProvider || 'openai'
  let apiKey = ''
  let model = ''

  switch (provider) {
    case 'openai':
      apiKey = settings.openaiKey
      model = settings.openaiModel || 'gpt-3.5-turbo'
      break
    case 'deepseek':
      apiKey = settings.deepseekKey
      model = settings.deepseekModel || 'deepseek-chat'
      break
    case 'gemini':
      apiKey = settings.geminiKey
      model = settings.geminiModel || 'gemini-pro'
      break
    case 'openrouter':
      apiKey = settings.openrouterKey
      model = settings.openrouterModel || 'anthropic/claude-3-haiku'
      break
    default:
      throw new Error(`Unsupported AI provider: ${provider}`)
  }

  if (!apiKey) {
    throw new Error(`API key not configured for ${provider}`)
  }

  return {
    name: provider,
    apiKey,
    model
  }
}

// Get available models for each provider
export function getAvailableModels(provider: string): string[] {
  switch (provider) {
    case 'openai':
      return [
        'gpt-3.5-turbo',
        'gpt-3.5-turbo-16k',
        'gpt-4',
        'gpt-4-turbo-preview',
        'gpt-4o',
        'gpt-4o-mini'
      ]
    case 'deepseek':
      return [
        'deepseek-chat',
        'deepseek-coder'
      ]
    case 'gemini':
      return [
        'gemini-pro',
        'gemini-pro-vision',
        'gemini-1.5-pro',
        'gemini-1.5-flash'
      ]
    case 'openrouter':
      return [
        'anthropic/claude-3-haiku',
        'anthropic/claude-3-sonnet',
        'anthropic/claude-3-opus',
        'openai/gpt-3.5-turbo',
        'openai/gpt-4',
        'openai/gpt-4-turbo',
        'google/gemini-pro',
        'meta-llama/llama-2-70b-chat',
        'mistralai/mixtral-8x7b-instruct',
        'cohere/command-r-plus'
      ]
    default:
      return []
  }
}

export async function generateRecipeIngredients(title: string, preferences?: any): Promise<GeneratedRecipe> {
  const provider = await getAIProvider()
  const aiService = new AIRecipeService(provider)
  
  return await aiService.generateRecipe(title, preferences)
}
