// Helper utilities for ViralChef extension
export interface StorageData {
  recipes: SavedRecipe[]
  searches: SearchHistory[]
  settings: UserSettings
  dailyStats: DailyStats
  viralFinds: ViralRecipe[]
}

export interface SavedRecipe {
  id: string
  title: string
  ingredients: string[]
  source: 'generated' | 'viral' | 'manual'
  sourceUrl?: string
  createdAt: string
  updatedAt: string
  tags: string[]
  isFavorite: boolean
  notes?: string
  difficulty?: 'Easy' | 'Medium' | 'Hard'
  prepTime?: string
  servings?: number
}

export interface SearchHistory {
  id: string
  query: string
  type: 'recipe' | 'viral'
  timestamp: string
  resultsCount: number
}

export interface UserSettings {
  language: 'en' | 'ar' | 'fr'
  aiProvider: 'openai' | 'deepseek' | 'gemini' | 'openrouter'
  aiModel?: string
  temperature?: number
  maxTokens?: number
  theme: 'light' | 'dark' | 'auto'
  notifications: boolean
  autoSave: boolean
  searchPreferences: {
    includeFacebook: boolean
    includeGoogle: boolean
    maxResults: number
    minEngagement: number
  }
  apiKeys: {
    openai?: string
    deepseek?: string
    gemini?: string
    openrouter?: string
    googleApiKey?: string
    googleSearchEngineId?: string
    facebookAccessToken?: string
  }
}

export interface DailyStats {
  date: string
  generated: number
  searched: number
  saved: number
}

export interface ViralRecipe {
  id: string
  title: string
  source: 'facebook' | 'google'
  url: string
  engagement: {
    likes?: number
    shares?: number
    comments?: number
    views?: number
  }
  discoveredAt: string
  confidence: number
  tags: string[]
}

// Storage utilities
export class StorageManager {
  static async get<T extends keyof StorageData>(keys: T[]): Promise<Partial<Pick<StorageData, T>>> {
    return new Promise((resolve) => {
      chrome.storage.local.get(keys, (result) => {
        resolve(result as Partial<Pick<StorageData, T>>)
      })
    })
  }

  static async set<T extends keyof StorageData>(data: Partial<Pick<StorageData, T>>): Promise<void> {
    return new Promise((resolve) => {
      chrome.storage.local.set(data, () => {
        resolve()
      })
    })
  }

  static async getSettings(): Promise<UserSettings> {
    const result = await this.get(['settings'])
    return result.settings || this.getDefaultSettings()
  }

  static async saveSettings(settings: Partial<UserSettings>): Promise<void> {
    const currentSettings = await this.getSettings()
    const updatedSettings = { ...currentSettings, ...settings }
    await this.set({ settings: updatedSettings })
  }

  static getDefaultSettings(): UserSettings {
    return {
      language: 'en',
      aiProvider: 'openai',
      aiModel: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 1500,
      theme: 'auto',
      notifications: true,
      autoSave: true,
      searchPreferences: {
        includeFacebook: true,
        includeGoogle: true,
        maxResults: 20,
        minEngagement: 50
      },
      apiKeys: {}
    }
  }

  static async saveRecipe(recipe: Omit<SavedRecipe, 'id' | 'createdAt' | 'updatedAt'>): Promise<SavedRecipe> {
    const recipes = await this.getRecipes()
    
    const newRecipe: SavedRecipe = {
      ...recipe,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    recipes.push(newRecipe)
    await this.set({ recipes })
    
    // Update daily stats
    await this.updateDailyStats('saved')
    
    return newRecipe
  }

  static async getRecipes(): Promise<SavedRecipe[]> {
    const result = await this.get(['recipes'])
    return result.recipes || []
  }

  static async deleteRecipe(id: string): Promise<void> {
    const recipes = await this.getRecipes()
    const filteredRecipes = recipes.filter(recipe => recipe.id !== id)
    await this.set({ recipes: filteredRecipes })
  }

  static async updateRecipe(id: string, updates: Partial<SavedRecipe>): Promise<void> {
    const recipes = await this.getRecipes()
    const index = recipes.findIndex(recipe => recipe.id === id)
    
    if (index !== -1) {
      recipes[index] = {
        ...recipes[index],
        ...updates,
        updatedAt: new Date().toISOString()
      }
      await this.set({ recipes })
    }
  }

  static async saveSearchHistory(query: string, type: 'recipe' | 'viral', resultsCount: number): Promise<void> {
    const searches = await this.getSearchHistory()
    
    const newSearch: SearchHistory = {
      id: this.generateId(),
      query,
      type,
      timestamp: new Date().toISOString(),
      resultsCount
    }
    
    searches.unshift(newSearch)
    
    // Keep only last 50 searches
    const trimmedSearches = searches.slice(0, 50)
    await this.set({ searches: trimmedSearches })
    
    // Update daily stats
    await this.updateDailyStats('searched')
  }

  static async getSearchHistory(): Promise<SearchHistory[]> {
    const result = await this.get(['searches'])
    return result.searches || []
  }

  static async updateDailyStats(action: 'generated' | 'searched' | 'saved'): Promise<void> {
    const today = new Date().toISOString().split('T')[0]
    const result = await this.get(['dailyStats'])
    
    let stats = result.dailyStats
    
    if (!stats || stats.date !== today) {
      stats = {
        date: today,
        generated: 0,
        searched: 0,
        saved: 0
      }
    }
    
    stats[action]++
    await this.set({ dailyStats: stats })
  }

  static async getDailyStats(): Promise<DailyStats> {
    const result = await this.get(['dailyStats'])
    const today = new Date().toISOString().split('T')[0]
    
    if (!result.dailyStats || result.dailyStats.date !== today) {
      return {
        date: today,
        generated: 0,
        searched: 0,
        saved: 0
      }
    }
    
    return result.dailyStats
  }

  static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }
}

// Text processing utilities
export class TextProcessor {
  static extractRecipeTitle(text: string): string {
    // Clean and format recipe title
    return text
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s-&]/g, '')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')
  }

  static extractIngredients(text: string): string[] {
    const lines = text.split('\n')
    const ingredients: string[] = []
    
    for (const line of lines) {
      const trimmed = line.trim()
      
      // Skip empty lines and headers
      if (!trimmed || trimmed.toLowerCase().includes('ingredient')) {
        continue
      }
      
      // Look for ingredient patterns
      if (this.isIngredientLine(trimmed)) {
        ingredients.push(this.cleanIngredient(trimmed))
      }
    }
    
    return ingredients
  }

  private static isIngredientLine(line: string): boolean {
    // Check if line looks like an ingredient
    const patterns = [
      /^\d+/, // Starts with number
      /^-/, // Starts with dash
      /^\*/, // Starts with asterisk
      /\d+\s*(cup|tbsp|tsp|oz|lb|g|kg|ml|l)/i, // Contains measurements
      /\b(cup|tablespoon|teaspoon|ounce|pound|gram|kilogram|milliliter|liter)s?\b/i
    ]
    
    return patterns.some(pattern => pattern.test(line))
  }

  private static cleanIngredient(ingredient: string): string {
    return ingredient
      .replace(/^[-*•]\s*/, '') // Remove bullet points
      .replace(/^\d+\.\s*/, '') // Remove numbering
      .trim()
  }

  static generateTags(title: string, ingredients: string[]): string[] {
    const tags = new Set<string>()
    
    // Extract tags from title
    const titleWords = title.toLowerCase().split(' ')
    const foodKeywords = [
      'chicken', 'beef', 'pork', 'fish', 'pasta', 'rice', 'bread',
      'cake', 'cookie', 'soup', 'salad', 'pizza', 'burger', 'sandwich'
    ]
    
    titleWords.forEach(word => {
      if (foodKeywords.includes(word)) {
        tags.add(word)
      }
    })
    
    // Extract tags from ingredients
    ingredients.forEach(ingredient => {
      const lowerIngredient = ingredient.toLowerCase()
      foodKeywords.forEach(keyword => {
        if (lowerIngredient.includes(keyword)) {
          tags.add(keyword)
        }
      })
    })
    
    // Add cooking method tags
    const cookingMethods = ['baked', 'grilled', 'fried', 'roasted', 'steamed']
    const titleLower = title.toLowerCase()
    cookingMethods.forEach(method => {
      if (titleLower.includes(method)) {
        tags.add(method)
      }
    })
    
    return Array.from(tags)
  }
}

// URL utilities
export class UrlUtils {
  static isValidUrl(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  static extractDomain(url: string): string {
    try {
      const urlObj = new URL(url)
      return urlObj.hostname.replace('www.', '')
    } catch {
      return url
    }
  }

  static buildShareUrl(recipe: SavedRecipe): string {
    const params = new URLSearchParams({
      title: recipe.title,
      ingredients: recipe.ingredients.join(', '),
      source: 'ViralChef'
    })
    
    return `https://viralchef.app/share?${params.toString()}`
  }
}

// Date utilities
export class DateUtils {
  static formatRelativeTime(date: string): string {
    const now = new Date()
    const past = new Date(date)
    const diffMs = now.getTime() - past.getTime()
    
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffMinutes < 1) return 'Just now'
    if (diffMinutes < 60) return `${diffMinutes}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`
    
    return past.toLocaleDateString()
  }

  static isToday(date: string): boolean {
    const today = new Date().toISOString().split('T')[0]
    const checkDate = new Date(date).toISOString().split('T')[0]
    return today === checkDate
  }

  static isThisWeek(date: string): boolean {
    const now = new Date()
    const past = new Date(date)
    const diffMs = now.getTime() - past.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    return diffDays <= 7
  }
}

// Validation utilities
export class ValidationUtils {
  static isValidRecipeTitle(title: string): boolean {
    return title.trim().length >= 3 && title.trim().length <= 100
  }

  static isValidIngredient(ingredient: string): boolean {
    return ingredient.trim().length >= 2 && ingredient.trim().length <= 200
  }

  static validateApiKey(provider: 'openai' | 'deepseek' | 'gemini' | 'openrouter', key: string): boolean {
    if (!key || key.trim().length === 0) return false

    switch (provider) {
      case 'openai':
        return key.startsWith('sk-') && key.length > 20
      case 'deepseek':
        return key.length > 10 // Basic validation for DeepSeek
      case 'gemini':
        return key.startsWith('AI') && key.length > 20 // Google AI API keys typically start with AI
      case 'openrouter':
        return key.startsWith('sk-or-') && key.length > 20 // OpenRouter keys start with sk-or-
      default:
        return false
    }
  }
}

// Export all utilities
export {
  StorageManager,
  TextProcessor,
  UrlUtils,
  DateUtils,
  ValidationUtils
}
