<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ViralChef - Enhanced AI Recipe Generator</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      width: 100vw;
      height: 100vh;
      background: transparent;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      color: #ffffff;
      overflow: hidden;
      margin: 0;
      padding: 0;
      position: fixed;
      top: 0;
      left: 0;
      z-index: 999999;
    }

    .sidebar-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(8px);
      z-index: 999998;
      animation: fadeIn 0.4s ease-out;
    }

    .container {
      position: fixed;
      top: 0;
      left: 0;
      width: 450px;
      height: 100vh;
      background: linear-gradient(135deg, #0f172a, #1e293b, #334155);
      backdrop-filter: blur(24px);
      border-right: 2px solid rgba(59, 130, 246, 0.3);
      box-shadow: 8px 0 32px rgba(0, 0, 0, 0.5);
      z-index: 999999;
      animation: slideInFromLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      overflow-y: auto;
      padding: 20px;
      box-sizing: border-box;
    }

    /* Animation for sliding sidebar from left */
    @keyframes slideInFromLeft {
      0% {
        transform: translateX(-100%);
        opacity: 0;
      }
      100% {
        transform: translateX(0);
        opacity: 1;
      }
    }

    /* Fade in animation for overlay */
    @keyframes fadeIn {
      0% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }

    /* Close button */
    .close-btn {
      position: absolute;
      top: 15px;
      right: 15px;
      width: 32px;
      height: 32px;
      background: rgba(239, 68, 68, 0.9);
      border: 1px solid rgba(239, 68, 68, 0.5);
      border-radius: 50%;
      color: white;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      z-index: 1000;
    }

    .close-btn:hover {
      background: rgba(239, 68, 68, 1);
      transform: scale(1.1);
      box-shadow: 0 4px 15px rgba(239, 68, 68, 0.5);
    }

    .header {
      text-align: center;
      margin-bottom: 20px;
      margin-top: 45px;
      padding: 0 20px;
    }

    .logo {
      width: 56px;
      height: 56px;
      margin: 0 auto 12px;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 28px;
      font-weight: bold;
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
      border: 1px solid rgba(59, 130, 246, 0.3);
      animation: float 3s ease-in-out infinite;
    }

    .title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 4px;
      background: linear-gradient(135deg, #3b82f6, #8b5cf6);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .subtitle {
      font-size: 12px;
      color: #94a3b8;
      font-weight: 500;
    }

    /* Floating animation */
    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-8px); }
    }
    
    .glass-card {
      background: rgba(15, 23, 42, 0.6);
      backdrop-filter: blur(20px);
      border-radius: 16px;
      border: 1px solid rgba(59, 130, 246, 0.2);
      padding: 20px;
      margin-bottom: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
      transition: all 0.3s ease;
    }

    .glass-card:hover {
      border-color: rgba(59, 130, 246, 0.4);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
      transform: translateY(-2px);
    }

    /* AI Status Indicator */
    .ai-status {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 16px 20px;
      padding: 12px 16px;
      background: rgba(15, 23, 42, 0.8);
      border: 1px solid rgba(59, 130, 246, 0.2);
      border-radius: 12px;
      font-size: 12px;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    .ai-status.connected {
      background: rgba(34, 197, 94, 0.1);
      border-color: rgba(34, 197, 94, 0.4);
      box-shadow: 0 0 20px rgba(34, 197, 94, 0.2);
    }

    .ai-status.error {
      background: rgba(239, 68, 68, 0.1);
      border-color: rgba(239, 68, 68, 0.4);
      box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
    }

    .ai-status.loading {
      background: rgba(251, 191, 36, 0.1);
      border-color: rgba(251, 191, 36, 0.4);
      box-shadow: 0 0 20px rgba(251, 191, 36, 0.2);
    }

    .ai-status-left {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .ai-status-icon {
      font-size: 14px;
      animation: pulse 2s infinite;
    }

    .ai-status-text {
      font-weight: 600;
      color: #e2e8f0;
    }

    .ai-status-model {
      font-size: 10px;
      color: #94a3b8;
      font-weight: 400;
    }

    .ai-status-right {
      font-size: 10px;
      color: #64748b;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.6; }
    }

    /* Navigation Tabs */
    .nav-tabs {
      display: flex;
      background: rgba(15, 23, 42, 0.6);
      border-radius: 12px;
      padding: 4px;
      margin: 16px 20px;
      border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .nav-tab {
      flex: 1;
      padding: 8px 12px;
      text-align: center;
      font-size: 11px;
      font-weight: 600;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      color: #94a3b8;
      border: none;
      background: transparent;
    }

    .nav-tab.active {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      color: white;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .nav-tab:hover:not(.active) {
      background: rgba(59, 130, 246, 0.1);
      color: #e2e8f0;
    }

    /* Tab Content */
    .tab-content {
      display: none;
      padding: 0 20px;
    }

    .tab-content.active {
      display: block;
      animation: fadeInUp 0.3s ease;
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .input-group {
      margin-bottom: 16px;
    }

    .input-group label {
      display: block;
      font-size: 12px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #e2e8f0;
    }

    .input-group input, .input-group select {
      width: 100%;
      padding: 12px 14px;
      border: 1px solid rgba(59, 130, 246, 0.3);
      border-radius: 10px;
      background: rgba(15, 23, 42, 0.8);
      backdrop-filter: blur(10px);
      font-size: 14px;
      color: #ffffff;
      transition: all 0.3s ease;
    }

    .input-group input::placeholder {
      color: #64748b;
    }

    .input-group input:focus, .input-group select:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
      background: rgba(15, 23, 42, 0.9);
    }
    
    .glass-button {
      width: 100%;
      padding: 14px 18px;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border: 1px solid rgba(59, 130, 246, 0.4);
      border-radius: 12px;
      color: white;
      font-weight: 600;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      margin-bottom: 12px;
      box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .glass-button:hover {
      background: linear-gradient(135deg, #2563eb, #1e40af);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
      border-color: rgba(59, 130, 246, 0.6);
    }

    .glass-button:active {
      transform: translateY(0);
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }

    .glass-button.secondary {
      background: linear-gradient(135deg, #6b7280, #4b5563);
      border-color: rgba(107, 114, 128, 0.4);
      box-shadow: 0 6px 20px rgba(107, 114, 128, 0.2);
    }

    .glass-button.secondary:hover {
      background: linear-gradient(135deg, #4b5563, #374151);
      box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
    }

    .glass-button.success {
      background: linear-gradient(135deg, #10b981, #059669);
      border-color: rgba(16, 185, 129, 0.4);
      box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
    }

    .glass-button.success:hover {
      background: linear-gradient(135deg, #059669, #047857);
      box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }

    .glass-button.danger {
      background: linear-gradient(135deg, #ef4444, #dc2626);
      border-color: rgba(239, 68, 68, 0.4);
      box-shadow: 0 6px 20px rgba(239, 68, 68, 0.3);
    }

    .glass-button.danger:hover {
      background: linear-gradient(135deg, #dc2626, #b91c1c);
      box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
    }
    
    .quick-actions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
      margin-top: 20px;
    }

    .quick-action {
      padding: 12px 8px;
      background: rgba(15, 23, 42, 0.6);
      border: 1px solid rgba(59, 130, 246, 0.3);
      border-radius: 10px;
      text-align: center;
      font-size: 11px;
      font-weight: 600;
      color: #e2e8f0;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
    }

    .quick-action:hover {
      background: rgba(59, 130, 246, 0.1);
      border-color: rgba(59, 130, 246, 0.5);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(59, 130, 246, 0.2);
    }

    .quick-action-icon {
      font-size: 16px;
    }

    /* Settings Panel */
    .settings-section {
      margin-bottom: 20px;
    }

    .settings-section h3 {
      font-size: 14px;
      font-weight: 700;
      color: #e2e8f0;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .provider-selector {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      margin-bottom: 16px;
    }

    .provider-option {
      padding: 10px;
      background: rgba(15, 23, 42, 0.6);
      border: 1px solid rgba(59, 130, 246, 0.2);
      border-radius: 8px;
      text-align: center;
      font-size: 11px;
      font-weight: 600;
      color: #94a3b8;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .provider-option.active {
      background: rgba(59, 130, 246, 0.2);
      border-color: rgba(59, 130, 246, 0.5);
      color: #3b82f6;
    }

    .provider-option:hover:not(.active) {
      background: rgba(59, 130, 246, 0.1);
      color: #e2e8f0;
    }

    .status {
      text-align: center;
      font-size: 12px;
      color: #64748b;
      margin-top: 16px;
      padding: 8px;
      background: rgba(15, 23, 42, 0.4);
      border-radius: 8px;
      border: 1px solid rgba(59, 130, 246, 0.1);
    }

    .status.success {
      color: #22c55e;
      border-color: rgba(34, 197, 94, 0.3);
      background: rgba(34, 197, 94, 0.1);
    }

    .status.error {
      color: #ef4444;
      border-color: rgba(239, 68, 68, 0.3);
      background: rgba(239, 68, 68, 0.1);
    }

    .loading {
      display: none;
      text-align: center;
      padding: 20px;
    }
    
    .spinner {
      width: 24px;
      height: 24px;
      border: 2px solid rgba(220, 38, 38, 0.3);
      border-top: 2px solid #dc2626;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 8px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="sidebar-overlay" id="overlay"></div>
  <div class="container">
    <div class="close-btn" id="closeBtn">×</div>

    <!-- Header -->
    <div class="header">
      <div class="logo">🍳</div>
      <div class="title">ViralChef</div>
      <div class="subtitle">Enhanced AI Recipe Generator</div>
    </div>

    <!-- AI Status -->
    <div class="ai-status loading" id="ai-status">
      <div class="ai-status-left">
        <span class="ai-status-icon">⏳</span>
        <div>
          <div class="ai-status-text">Checking AI Status...</div>
          <div class="ai-status-model" id="ai-model">Loading...</div>
        </div>
      </div>
      <div class="ai-status-right" id="ai-last-used">--</div>
    </div>

    <!-- Navigation Tabs -->
    <div class="nav-tabs">
      <button class="nav-tab active" data-tab="generate">🤖 Generate</button>
      <button class="nav-tab" data-tab="search">🔍 Search</button>
      <button class="nav-tab" data-tab="settings">⚙️ Settings</button>
    </div>

    <!-- Generate Tab -->
    <div class="tab-content active" id="generate-tab">
      <div class="glass-card">
        <div class="input-group">
          <label for="recipe-title">Recipe Title</label>
          <input type="text" id="recipe-title" placeholder="e.g., Chocolate Chip Cookies">
        </div>
        <button class="glass-button" id="generate-btn">
          🤖 Generate Ingredients
        </button>
      </div>
    </div>

    <!-- Search Tab -->
    <div class="tab-content" id="search-tab">
      <div class="glass-card">
        <div class="input-group">
          <label for="search-query">Search Viral Recipes</label>
          <input type="text" id="search-query" placeholder="e.g., viral desserts 2024">
        </div>
        <button class="glass-button secondary" id="search-btn">
          🔥 Find Viral Recipes
        </button>
      </div>
    </div>

    <!-- Settings Tab -->
    <div class="tab-content" id="settings-tab">
      <div class="glass-card">
        <div class="settings-section">
          <h3>🤖 AI Provider</h3>
          <div class="provider-selector">
            <div class="provider-option active" data-provider="openai">
              OpenAI
            </div>
            <div class="provider-option" data-provider="deepseek">
              DeepSeek
            </div>
            <div class="provider-option" data-provider="gemini">
              Gemini
            </div>
            <div class="provider-option" data-provider="openrouter">
              OpenRouter
            </div>
          </div>
        </div>

        <div class="settings-section">
          <h3>🔑 API Key</h3>
          <div class="input-group">
            <input type="password" id="api-key" placeholder="Enter your API key">
          </div>
        </div>

        <div class="settings-section">
          <h3>🎛️ Model Settings</h3>
          <div class="input-group">
            <label for="model-select">Model</label>
            <select id="model-select">
              <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
              <option value="gpt-4">GPT-4</option>
              <option value="gpt-4-turbo">GPT-4 Turbo</option>
            </select>
          </div>
          <div class="input-group">
            <label for="temperature">Temperature: <span id="temp-value">0.7</span></label>
            <input type="range" id="temperature" min="0" max="1" step="0.1" value="0.7">
          </div>
        </div>

        <button class="glass-button success" id="save-settings">
          💾 Save Settings
        </button>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <div class="quick-action" id="dashboard-action">
        <div class="quick-action-icon">📊</div>
        <div>Dashboard</div>
      </div>
      <div class="quick-action" id="saved-recipes">
        <div class="quick-action-icon">📚</div>
        <div>Saved</div>
      </div>
      <div class="quick-action" id="trending">
        <div class="quick-action-icon">📈</div>
        <div>Trending</div>
      </div>
      <div class="quick-action" id="favorites">
        <div class="quick-action-icon">❤️</div>
        <div>Favorites</div>
      </div>
    </div>

    <!-- Status Display -->
    <div class="status" id="status">Ready to generate amazing recipes!</div>

    <!-- Loading Indicator -->
    <div class="loading" id="loading">
      <div class="spinner"></div>
      <div>Processing your request...</div>
    </div>
    
    <div class="status" id="status">
      Ready to generate amazing recipes!
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
