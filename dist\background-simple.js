// ViralChef Extension Background Script - Enhanced Version
console.log('ViralChef Extension Background Script Loaded - Enhanced with Multiple LLM Support');

// AI Service Integration
class AIServiceManager {
  constructor() {
    this.supportedProviders = ['openai', 'deepseek', 'gemini', 'openrouter'];
  }

  async getActiveProvider() {
    const settings = await chrome.storage.sync.get(['aiProvider', 'openaiKey', 'deepseekKey', 'geminiKey', 'openrouterKey']);
    const provider = settings.aiProvider || 'openai';

    const apiKey = settings[`${provider}Key`] || settings[`${provider === 'openai' ? 'openai' : provider}Key`];

    if (!apiKey) {
      throw new Error(`API key not configured for ${provider}`);
    }

    return { provider, apiKey, settings };
  }

  async generateRecipe(title, preferences = {}) {
    try {
      const { provider, apiKey, settings } = await this.getActiveProvider();

      switch (provider) {
        case 'openai':
          return await this.callOpenAI(apiKey, title, preferences, settings);
        case 'deepseek':
          return await this.callDeepSeek(apiKey, title, preferences, settings);
        case 'gemini':
          return await this.callGemini(apiKey, title, preferences, settings);
        case 'openrouter':
          return await this.callOpenRouter(apiKey, title, preferences, settings);
        default:
          throw new Error(`Unsupported provider: ${provider}`);
      }
    } catch (error) {
      console.error('Recipe generation failed:', error);
      throw error;
    }
  }

  async callOpenAI(apiKey, title, preferences, settings) {
    const model = settings.openaiModel || 'gpt-3.5-turbo';
    const temperature = settings.temperature || 0.7;
    const maxTokens = settings.maxTokens || 1500;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        messages: [
          {
            role: 'system',
            content: 'You are a professional chef and recipe developer. Generate detailed, accurate recipes with precise measurements and helpful tips.'
          },
          {
            role: 'user',
            content: this.buildRecipePrompt(title, preferences)
          }
        ],
        temperature,
        max_tokens: maxTokens
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    return this.parseRecipeResponse(data.choices[0].message.content, title);
  }

  async callDeepSeek(apiKey, title, preferences, settings) {
    const model = settings.deepseekModel || 'deepseek-chat';
    const temperature = settings.temperature || 0.7;
    const maxTokens = settings.maxTokens || 1500;

    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        messages: [
          {
            role: 'system',
            content: 'You are a professional chef and recipe developer. Generate detailed, accurate recipes with precise measurements and helpful tips.'
          },
          {
            role: 'user',
            content: this.buildRecipePrompt(title, preferences)
          }
        ],
        temperature,
        max_tokens: maxTokens
      })
    });

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status}`);
    }

    const data = await response.json();
    return this.parseRecipeResponse(data.choices[0].message.content, title);
  }

  async callGemini(apiKey, title, preferences, settings) {
    const model = settings.geminiModel || 'gemini-pro';
    const temperature = settings.temperature || 0.7;
    const maxTokens = settings.maxTokens || 1500;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `You are a professional chef and recipe developer. Generate detailed, accurate recipes with precise measurements and helpful tips.\n\n${this.buildRecipePrompt(title, preferences)}`
          }]
        }],
        generationConfig: {
          temperature,
          maxOutputTokens: maxTokens
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status}`);
    }

    const data = await response.json();
    const content = data.candidates?.[0]?.content?.parts?.[0]?.text;
    if (!content) {
      throw new Error('No content received from Gemini');
    }

    return this.parseRecipeResponse(content, title);
  }

  async callOpenRouter(apiKey, title, preferences, settings) {
    const model = settings.openrouterModel || 'anthropic/claude-3-haiku';
    const temperature = settings.temperature || 0.7;
    const maxTokens = settings.maxTokens || 1500;

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://viralchef.extension',
        'X-Title': 'ViralChef Recipe Generator'
      },
      body: JSON.stringify({
        model,
        messages: [
          {
            role: 'system',
            content: 'You are a professional chef and recipe developer. Generate detailed, accurate recipes with precise measurements and helpful tips.'
          },
          {
            role: 'user',
            content: this.buildRecipePrompt(title, preferences)
          }
        ],
        temperature,
        max_tokens: maxTokens
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }

    const data = await response.json();
    return this.parseRecipeResponse(data.choices[0].message.content, title);
  }

  buildRecipePrompt(title, preferences) {
    let prompt = `Generate a complete recipe for "${title}". Please provide:

1. A list of ingredients with precise measurements
2. Estimated serving size
3. Preparation time
4. Difficulty level (Easy/Medium/Hard)
5. Cuisine type
6. Dietary information (vegetarian, gluten-free, etc.)
7. Helpful cooking tips
8. Basic nutritional notes

Format the response as JSON with the following structure:
{
  "ingredients": [{"name": "ingredient", "amount": "1", "unit": "cup", "notes": "optional"}],
  "servings": 4,
  "prepTime": "30 minutes",
  "difficulty": "Easy",
  "cuisine": "Italian",
  "dietaryInfo": ["vegetarian"],
  "tips": ["tip1", "tip2"],
  "nutritionNotes": "High in protein"
}`;

    if (preferences.dietary) {
      prompt += `\n\nDietary restrictions: ${preferences.dietary.join(', ')}`;
    }
    if (preferences.servings) {
      prompt += `\nServing size: ${preferences.servings} people`;
    }
    if (preferences.difficulty) {
      prompt += `\nPreferred difficulty: ${preferences.difficulty}`;
    }

    return prompt;
  }

  parseRecipeResponse(response, title) {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          title,
          ingredients: parsed.ingredients || [],
          servings: parsed.servings || 4,
          prepTime: parsed.prepTime || '30 minutes',
          difficulty: parsed.difficulty || 'Medium',
          cuisine: parsed.cuisine || 'International',
          dietaryInfo: parsed.dietaryInfo || [],
          tips: parsed.tips || [],
          nutritionNotes: parsed.nutritionNotes
        };
      }
    } catch (error) {
      console.error('Failed to parse recipe JSON:', error);
    }

    return {
      title,
      ingredients: [
        { name: 'Main ingredient (please specify)', amount: '1', unit: 'portion' },
        { name: 'Salt', amount: '1', unit: 'pinch' },
        { name: 'Pepper', amount: '1', unit: 'pinch' }
      ],
      servings: 2,
      prepTime: '15 minutes',
      difficulty: 'Easy',
      cuisine: 'International',
      dietaryInfo: [],
      tips: ['This is a basic template. Please regenerate with a more specific title.'],
      nutritionNotes: 'Nutritional information varies by ingredients used'
    };
  }
}

// Initialize AI Service Manager
const aiService = new AIServiceManager();

// Handle action button click to open sidebar
chrome.action.onClicked.addListener(async (tab) => {
  console.log('Extension icon clicked, tab:', tab);

  // Check if the URL is accessible
  if (tab.url.startsWith('chrome://') ||
      tab.url.startsWith('chrome-extension://') ||
      tab.url.startsWith('edge://') ||
      tab.url.startsWith('about:') ||
      tab.url.startsWith('moz-extension://')) {

    console.log('Cannot inject script into protected page:', tab.url);

    // Show notification to user
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'assets/icons/icon48.png',
      title: 'ViralChef',
      message: 'Cannot open sidebar on this page. Please visit a regular website (like google.com) and try again.'
    });

    // Open dashboard instead
    chrome.tabs.create({
      url: chrome.runtime.getURL('dashboard/index.html')
    });
    return;
  }

  try {
    console.log('Attempting to inject script into:', tab.url);

    // Simple test - just inject an alert first
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: () => {
        console.log('Script injected successfully!');
        
        // Remove existing sidebar if any
        const existing = document.getElementById('viralchef-test-sidebar');
        if (existing) {
          existing.remove();
          return;
        }
        
        // Create simple test sidebar
        const sidebar = document.createElement('div');
        sidebar.id = 'viralchef-test-sidebar';
        sidebar.style.cssText = `
          position: fixed !important;
          top: 0 !important;
          left: 0 !important;
          width: 300px !important;
          height: 100vh !important;
          background: #1a1a1a !important;
          border-right: 2px solid #dc2626 !important;
          z-index: 999999 !important;
          color: white !important;
          padding: 20px !important;
          box-sizing: border-box !important;
          font-family: Arial, sans-serif !important;
          transform: translateX(-100%) !important;
          transition: transform 0.5s ease !important;
        `;
        
        sidebar.innerHTML = `
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="color: #dc2626; margin: 0;">🍳 ViralChef</h2>
            <p style="margin: 10px 0; font-size: 14px;">AI Recipe Generator</p>
          </div>
          
          <div style="margin-bottom: 20px;">
            <input type="text" placeholder="Enter recipe title..." style="
              width: 100%;
              padding: 10px;
              border: 1px solid #dc2626;
              border-radius: 5px;
              background: #2d1b1b;
              color: white;
              box-sizing: border-box;
            ">
          </div>
          
          <button onclick="alert('Generate clicked!')" style="
            width: 100%;
            padding: 12px;
            background: #dc2626;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
          ">Generate Recipe</button>
          
          <button onclick="document.getElementById('viralchef-test-sidebar').remove()" style="
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            background: #dc2626;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
          ">×</button>
        `;
        
        document.body.appendChild(sidebar);
        
        // Animate in
        setTimeout(() => {
          sidebar.style.transform = 'translateX(0)';
        }, 100);
        
        console.log('Test sidebar created successfully!');
      }
    });
    
    console.log('Script injection completed');
    
  } catch (error) {
    console.error('Failed to inject script:', error);
    console.error('Error details:', error.message);
  }
});

// Handle messages from content script or popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Message received:', request);
  
  if (request.action === 'openDashboard') {
    chrome.tabs.create({
      url: chrome.runtime.getURL('dashboard/index.html')
    });
    sendResponse({ success: true });
    return;
  }

  // Handle recipe generation with enhanced AI support
  if (request.action === 'generateRecipe') {
    (async () => {
      try {
        const { title, preferences } = request.data;
        console.log('Generating recipe with enhanced AI service:', title);

        const recipe = await aiService.generateRecipe(title, preferences);

        // Save to storage
        const recipes = await chrome.storage.local.get(['recipes']);
        const savedRecipes = recipes.recipes || [];

        const newRecipe = {
          id: Date.now().toString(),
          ...recipe,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        savedRecipes.push(newRecipe);
        await chrome.storage.local.set({ recipes: savedRecipes });

        // Update daily stats
        const today = new Date().toDateString();
        const stats = await chrome.storage.local.get(['dailyStats']);
        const dailyStats = stats.dailyStats || {};

        if (!dailyStats[today]) {
          dailyStats[today] = { generated: 0, searched: 0, saved: 0 };
        }
        dailyStats[today].generated++;

        await chrome.storage.local.set({ dailyStats });

        // Add to recent activity
        const activity = await chrome.storage.local.get(['recentActivity']);
        const recentActivity = activity.recentActivity || [];

        recentActivity.unshift({
          id: Date.now().toString(),
          type: 'recipe',
          title: `Generated ${title}`,
          time: 'Just now'
        });

        // Keep only last 10 activities
        if (recentActivity.length > 10) {
          recentActivity.splice(10);
        }

        await chrome.storage.local.set({ recentActivity });

        sendResponse({ success: true, recipe: newRecipe });
      } catch (error) {
        console.error('Recipe generation failed:', error);
        sendResponse({ success: false, error: error.message });
      }
    })();
    return true; // Keep message channel open for async response
  }

  // Handle viral recipe search
  if (request.action === 'searchViralRecipes') {
    (async () => {
      try {
        const { query } = request.data;
        console.log('Searching viral recipes:', query);

        // This would integrate with the existing viral search functionality
        // For now, return a placeholder response
        const results = {
          query,
          results: [],
          timestamp: new Date().toISOString()
        };

        // Update daily stats
        const today = new Date().toDateString();
        const stats = await chrome.storage.local.get(['dailyStats']);
        const dailyStats = stats.dailyStats || {};

        if (!dailyStats[today]) {
          dailyStats[today] = { generated: 0, searched: 0, saved: 0 };
        }
        dailyStats[today].searched++;

        await chrome.storage.local.set({ dailyStats });

        // Add to recent activity
        const activity = await chrome.storage.local.get(['recentActivity']);
        const recentActivity = activity.recentActivity || [];

        recentActivity.unshift({
          id: Date.now().toString(),
          type: 'search',
          title: `Searched "${query}"`,
          time: 'Just now'
        });

        // Keep only last 10 activities
        if (recentActivity.length > 10) {
          recentActivity.splice(10);
        }

        await chrome.storage.local.set({ recentActivity });

        sendResponse({ success: true, results });
      } catch (error) {
        console.error('Viral search failed:', error);
        sendResponse({ success: false, error: error.message });
      }
    })();
    return true; // Keep message channel open for async response
  }

  // Handle AI provider status check
  if (request.action === 'checkAIStatus') {
    (async () => {
      try {
        const { provider, apiKey, settings } = await aiService.getActiveProvider();
        sendResponse({
          success: true,
          status: {
            provider,
            connected: true,
            model: settings[`${provider}Model`] || 'default',
            lastUsed: new Date().toLocaleTimeString()
          }
        });
      } catch (error) {
        sendResponse({
          success: false,
          status: {
            provider: 'unknown',
            connected: false,
            error: error.message
          }
        });
      }
    })();
    return true;
  }

  sendResponse({ success: true });
});
