import React, { useState, useEffect } from 'react'
import { ChefHat, Search, Settings, Heart, BookOpen, TrendingUp, Sparkles, Zap, CheckCircle, XCircle, Clock, Brain } from 'lucide-react'
import RecipeGenerator from '../components/RecipeGenerator'
import ViralSearch from '../components/ViralSearch'
import SettingsPanel from '../components/SettingsPanel'
import SavedRecipes from '../components/SavedRecipes'

type TabType = 'generate' | 'search' | 'saved' | 'trending' | 'settings'

interface DashboardProps {}

const Dashboard: React.FC<DashboardProps> = () => {
  const [activeTab, setActiveTab] = useState<TabType>('generate')
  const [isLoading, setIsLoading] = useState(false)
  const [stats, setStats] = useState({
    savedRecipes: 0,
    generatedToday: 0,
    viralFinds: 0
  })

  useEffect(() => {
    loadStats()
    
    // Check for URL parameters to set initial tab
    const urlParams = new URLSearchParams(window.location.search)
    const tab = urlParams.get('tab') as TabType
    if (tab && ['generate', 'search', 'saved', 'trending', 'settings'].includes(tab)) {
      setActiveTab(tab)
    }
  }, [])

  const loadStats = async () => {
    try {
      const data = await chrome.storage.local.get(['recipes', 'dailyStats', 'viralFinds'])
      
      setStats({
        savedRecipes: data.recipes?.length || 0,
        generatedToday: data.dailyStats?.generated || 0,
        viralFinds: data.viralFinds?.length || 0
      })
    } catch (error) {
      console.error('Failed to load stats:', error)
    }
  }

  const tabs = [
    { id: 'generate', label: 'Generate', icon: ChefHat, color: 'from-emerald-400 to-teal-500' },
    { id: 'search', label: 'Viral Search', icon: Search, color: 'from-blue-400 to-indigo-500' },
    { id: 'saved', label: 'Saved', icon: BookOpen, color: 'from-purple-400 to-pink-500' },
    { id: 'trending', label: 'Trending', icon: TrendingUp, color: 'from-orange-400 to-red-500' },
    { id: 'settings', label: 'Settings', icon: Settings, color: 'from-gray-400 to-gray-600' }
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 'generate':
        return <RecipeGenerator onLoadingChange={setIsLoading} onStatsUpdate={loadStats} />
      case 'search':
        return <ViralSearch onLoadingChange={setIsLoading} onStatsUpdate={loadStats} />
      case 'saved':
        return <SavedRecipes onStatsUpdate={loadStats} />
      case 'trending':
        return <TrendingRecipes />
      case 'settings':
        return <SettingsPanel />
      default:
        return <RecipeGenerator onLoadingChange={setIsLoading} onStatsUpdate={loadStats} />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gradient-green via-gradient-gray to-gradient-blue">
      {/* Header */}
      <header className="glass-nav sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-xl flex items-center justify-center">
                <ChefHat className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold gradient-text">ViralChef</h1>
                <p className="text-xs text-gray-600">AI Recipe Generator</p>
              </div>
            </div>

            {/* Stats */}
            <div className="hidden md:flex items-center space-x-6">
              <div className="text-center">
                <div className="text-lg font-bold text-gray-800">{stats.savedRecipes}</div>
                <div className="text-xs text-gray-600">Saved</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-800">{stats.generatedToday}</div>
                <div className="text-xs text-gray-600">Today</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-800">{stats.viralFinds}</div>
                <div className="text-xs text-gray-600">Viral</div>
              </div>
            </div>

            {/* Loading indicator */}
            {isLoading && (
              <div className="flex items-center space-x-2">
                <div className="loading-spinner"></div>
                <span className="text-sm text-gray-600">Processing...</span>
              </div>
            )}
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar Navigation */}
        <nav className="glass-sidebar w-[350px] min-h-screen p-6">
          <div className="space-y-2">
            {tabs.map((tab) => {
              const Icon = tab.icon
              const isActive = activeTab === tab.id

              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as TabType)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 ${
                    isActive
                      ? `bg-gradient-to-r ${tab.color} text-white shadow-lg transform scale-105`
                      : 'text-gray-700 hover:bg-white/20 hover:scale-102'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{tab.label}</span>
                  {isActive && <Sparkles className="w-4 h-4 ml-auto animate-pulse" />}
                </button>
              )
            })}
          </div>

          {/* AI Provider Status */}
          <div className="mt-6 pt-4 border-t border-white/20">
            <h3 className="text-sm font-semibold text-gray-600 mb-3">AI Provider Status</h3>
            <AIProviderStatus />
          </div>

          {/* Quick Actions */}
          <div className="mt-6 pt-4 border-t border-white/20">
            <h3 className="text-sm font-semibold text-gray-600 mb-3">Quick Actions</h3>
            <div className="space-y-2">
              <button className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:bg-white/10 rounded-lg transition-colors">
                <Heart className="w-4 h-4" />
                <span>Favorites</span>
              </button>
              <button className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:bg-white/10 rounded-lg transition-colors">
                <TrendingUp className="w-4 h-4" />
                <span>Popular Today</span>
              </button>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="mt-6 pt-4 border-t border-white/20">
            <h3 className="text-sm font-semibold text-gray-600 mb-3">Recent Activity</h3>
            <RecentActivity />
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 p-6">
          <div className="max-w-6xl mx-auto">
            {renderTabContent()}
          </div>
        </main>
      </div>
    </div>
  )
}

// AI Provider Status Component
const AIProviderStatus: React.FC = () => {
  const [providerStatus, setProviderStatus] = useState<{
    current: string
    status: 'connected' | 'error' | 'loading'
    model: string
    lastUsed: string
  }>({
    current: 'Loading...',
    status: 'loading',
    model: '',
    lastUsed: ''
  })

  useEffect(() => {
    loadProviderStatus()
  }, [])

  const loadProviderStatus = async () => {
    try {
      const settings = await chrome.storage.sync.get(['aiProvider', 'openaiKey', 'deepseekKey', 'geminiKey', 'openrouterKey'])
      const provider = settings.aiProvider || 'openai'

      let hasKey = false
      let model = ''

      switch (provider) {
        case 'openai':
          hasKey = !!settings.openaiKey
          model = 'GPT-3.5 Turbo'
          break
        case 'deepseek':
          hasKey = !!settings.deepseekKey
          model = 'DeepSeek Chat'
          break
        case 'gemini':
          hasKey = !!settings.geminiKey
          model = 'Gemini Pro'
          break
        case 'openrouter':
          hasKey = !!settings.openrouterKey
          model = 'Multiple Models'
          break
      }

      setProviderStatus({
        current: provider.charAt(0).toUpperCase() + provider.slice(1),
        status: hasKey ? 'connected' : 'error',
        model,
        lastUsed: new Date().toLocaleTimeString()
      })
    } catch (error) {
      setProviderStatus(prev => ({ ...prev, status: 'error' }))
    }
  }

  const getStatusIcon = () => {
    switch (providerStatus.status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'loading':
        return <Clock className="w-4 h-4 text-yellow-500 animate-spin" />
    }
  }

  const getStatusColor = () => {
    switch (providerStatus.status) {
      case 'connected':
        return 'bg-green-100/50 border-green-200/50 animate-pulse-glow'
      case 'error':
        return 'bg-red-100/50 border-red-200/50 animate-error-pulse'
      case 'loading':
        return 'bg-yellow-100/50 border-yellow-200/50'
    }
  }

  return (
    <div className={`ai-status-card p-3 rounded-lg border ${getStatusColor()}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <Brain className="w-4 h-4 text-gray-600" />
          <span className="text-sm font-medium text-gray-700">{providerStatus.current}</span>
        </div>
        {getStatusIcon()}
      </div>
      <div className="text-xs text-gray-600">
        <div>Model: {providerStatus.model}</div>
        {providerStatus.status === 'connected' && (
          <div>Last used: {providerStatus.lastUsed}</div>
        )}
        {providerStatus.status === 'error' && (
          <div className="text-red-600">API key required</div>
        )}
      </div>
    </div>
  )
}

// Recent Activity Component
const RecentActivity: React.FC = () => {
  const [activities, setActivities] = useState<Array<{
    id: string
    type: 'recipe' | 'search' | 'save'
    title: string
    time: string
  }>>([])

  useEffect(() => {
    loadRecentActivity()
  }, [])

  const loadRecentActivity = async () => {
    try {
      const data = await chrome.storage.local.get(['recentActivity'])
      setActivities(data.recentActivity || [
        { id: '1', type: 'recipe', title: 'Generated Pasta Recipe', time: '2 min ago' },
        { id: '2', type: 'search', title: 'Searched viral desserts', time: '5 min ago' },
        { id: '3', type: 'save', title: 'Saved Chocolate Cake', time: '10 min ago' }
      ])
    } catch (error) {
      console.error('Failed to load recent activity:', error)
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'recipe':
        return <ChefHat className="w-3 h-3 text-emerald-500" />
      case 'search':
        return <Search className="w-3 h-3 text-blue-500" />
      case 'save':
        return <Heart className="w-3 h-3 text-pink-500" />
      default:
        return <Zap className="w-3 h-3 text-gray-500" />
    }
  }

  return (
    <div className="space-y-2">
      {activities.slice(0, 3).map((activity) => (
        <div key={activity.id} className="recent-activity-item flex items-center space-x-2 p-2 rounded-lg transition-colors cursor-pointer">
          {getActivityIcon(activity.type)}
          <div className="flex-1 min-w-0">
            <div className="text-xs font-medium text-gray-700 truncate">{activity.title}</div>
            <div className="text-xs text-gray-500">{activity.time}</div>
          </div>
        </div>
      ))}
      {activities.length === 0 && (
        <div className="text-xs text-gray-500 text-center py-2">
          No recent activity
        </div>
      )}
    </div>
  )
}

// Placeholder component for trending recipes
const TrendingRecipes: React.FC = () => {
  return (
    <div className="glass-card p-8 text-center">
      <TrendingUp className="w-16 h-16 mx-auto mb-4 text-orange-500" />
      <h2 className="text-2xl font-bold text-gray-800 mb-2">Trending Recipes</h2>
      <p className="text-gray-600 mb-6">Discover what's popular in the culinary world right now</p>
      <div className="text-sm text-gray-500">
        This feature will show trending recipes from social media and cooking websites.
      </div>
    </div>
  )
}

export default Dashboard
