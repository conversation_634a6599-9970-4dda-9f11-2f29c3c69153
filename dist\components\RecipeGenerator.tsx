import React, { useState, useEffect } from 'react'
import { <PERSON>Hat, <PERSON><PERSON><PERSON>, <PERSON>, Users, Star, Save, Share2, RefreshCw } from 'lucide-react'
import { GeneratedRecipe, generateRecipeIngredients } from '../utils/ai'
import { StorageManager, TextProcessor } from '../utils/helpers'
import GlassButton from './GlassButton'

interface RecipeGeneratorProps {
  onLoadingChange: (loading: boolean) => void
  onStatsUpdate: () => void
}

const RecipeGenerator: React.FC<RecipeGeneratorProps> = ({ onLoadingChange, onStatsUpdate }) => {
  const [title, setTitle] = useState('')
  const [generatedRecipe, setGeneratedRecipe] = useState<GeneratedRecipe | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [preferences, setPreferences] = useState({
    servings: 4,
    difficulty: 'Medium' as 'Easy' | 'Medium' | 'Hard',
    dietary: [] as string[]
  })
  const [recentTitles, setRecentTitles] = useState<string[]>([])

  useEffect(() => {
    loadRecentTitles()
  }, [])

  const loadRecentTitles = async () => {
    try {
      const searches = await StorageManager.getSearchHistory()
      const recipeTitles = searches
        .filter(search => search.type === 'recipe')
        .map(search => search.query)
        .slice(0, 5)
      setRecentTitles(recipeTitles)
    } catch (error) {
      console.error('Failed to load recent titles:', error)
    }
  }

  const handleGenerate = async () => {
    if (!title.trim()) {
      setError('Please enter a recipe title')
      return
    }

    setIsGenerating(true)
    setError(null)
    onLoadingChange(true)

    try {
      const recipe = await generateRecipeIngredients(title, preferences)
      setGeneratedRecipe(recipe)
      
      // Save to search history
      await StorageManager.saveSearchHistory(title, 'recipe', recipe.ingredients.length)
      await StorageManager.updateDailyStats('generated')
      
      onStatsUpdate()
      loadRecentTitles()
    } catch (error) {
      console.error('Recipe generation failed:', error)
      setError(error instanceof Error ? error.message : 'Failed to generate recipe')
    } finally {
      setIsGenerating(false)
      onLoadingChange(false)
    }
  }

  const handleSave = async () => {
    if (!generatedRecipe) return

    try {
      const tags = TextProcessor.generateTags(generatedRecipe.title, 
        generatedRecipe.ingredients.map(ing => ing.name))
      
      await StorageManager.saveRecipe({
        title: generatedRecipe.title,
        ingredients: generatedRecipe.ingredients.map(ing => 
          `${ing.amount} ${ing.unit} ${ing.name}${ing.notes ? ` (${ing.notes})` : ''}`
        ),
        source: 'generated',
        tags,
        isFavorite: false,
        difficulty: generatedRecipe.difficulty,
        prepTime: generatedRecipe.prepTime,
        servings: generatedRecipe.servings
      })
      
      onStatsUpdate()
      
      // Show success feedback
      setError(null)
    } catch (error) {
      console.error('Failed to save recipe:', error)
      setError('Failed to save recipe')
    }
  }

  const handleShare = async () => {
    if (!generatedRecipe) return

    try {
      const shareText = `🍳 ${generatedRecipe.title}\n\nIngredients:\n${
        generatedRecipe.ingredients.map(ing => 
          `• ${ing.amount} ${ing.unit} ${ing.name}`
        ).join('\n')
      }\n\nGenerated with ViralChef AI`

      if (navigator.share) {
        await navigator.share({
          title: generatedRecipe.title,
          text: shareText
        })
      } else {
        await navigator.clipboard.writeText(shareText)
        // Show copied feedback
      }
    } catch (error) {
      console.error('Failed to share recipe:', error)
    }
  }

  const quickTitles = [
    'Creamy Garlic Pasta',
    'Honey Glazed Chicken',
    'Chocolate Chip Cookies',
    'Beef Stir Fry',
    'Vegetarian Tacos'
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center space-x-3 mb-4">
          <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center animate-float">
            <ChefHat className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold gradient-text">AI Recipe Generator</h1>
            <p className="text-gray-600">Transform any recipe title into complete ingredients list</p>
          </div>
        </div>
      </div>

      {/* Input Section */}
      <div className="glass-card p-6">
        <div className="space-y-4">
          <div>
            <label htmlFor="recipe-title" className="block text-sm font-medium text-gray-700 mb-2">
              Recipe Title
            </label>
            <input
              id="recipe-title"
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="e.g., Spicy Tuna Pasta with Garlic"
              className="glass-input w-full text-lg"
              onKeyPress={(e) => e.key === 'Enter' && handleGenerate()}
              disabled={isGenerating}
            />
          </div>

          {/* Quick Title Suggestions */}
          {!title && (
            <div>
              <p className="text-sm text-gray-600 mb-2">Quick suggestions:</p>
              <div className="flex flex-wrap gap-2">
                {quickTitles.map((quickTitle) => (
                  <button
                    key={quickTitle}
                    onClick={() => setTitle(quickTitle)}
                    className="px-3 py-1 text-sm bg-white/30 hover:bg-white/40 rounded-full border border-white/40 transition-colors"
                  >
                    {quickTitle}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Recent Titles */}
          {recentTitles.length > 0 && !title && (
            <div>
              <p className="text-sm text-gray-600 mb-2">Recent searches:</p>
              <div className="flex flex-wrap gap-2">
                {recentTitles.map((recentTitle, index) => (
                  <button
                    key={index}
                    onClick={() => setTitle(recentTitle)}
                    className="px-3 py-1 text-sm bg-blue-100/50 hover:bg-blue-100/70 rounded-full border border-blue-200/50 transition-colors"
                  >
                    {recentTitle}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Preferences */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Servings</label>
              <select
                value={preferences.servings}
                onChange={(e) => setPreferences(prev => ({ ...prev, servings: parseInt(e.target.value) }))}
                className="glass-input w-full"
              >
                <option value={2}>2 people</option>
                <option value={4}>4 people</option>
                <option value={6}>6 people</option>
                <option value={8}>8 people</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Difficulty</label>
              <select
                value={preferences.difficulty}
                onChange={(e) => setPreferences(prev => ({ ...prev, difficulty: e.target.value as any }))}
                className="glass-input w-full"
              >
                <option value="Easy">Easy</option>
                <option value="Medium">Medium</option>
                <option value="Hard">Hard</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Dietary</label>
              <select
                onChange={(e) => {
                  const value = e.target.value
                  if (value && !preferences.dietary.includes(value)) {
                    setPreferences(prev => ({ 
                      ...prev, 
                      dietary: [...prev.dietary, value] 
                    }))
                  }
                }}
                className="glass-input w-full"
              >
                <option value="">Add dietary restriction</option>
                <option value="vegetarian">Vegetarian</option>
                <option value="vegan">Vegan</option>
                <option value="gluten-free">Gluten-free</option>
                <option value="dairy-free">Dairy-free</option>
                <option value="keto">Keto</option>
                <option value="low-carb">Low-carb</option>
              </select>
            </div>
          </div>

          {/* Dietary Tags */}
          {preferences.dietary.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {preferences.dietary.map((diet) => (
                <span
                  key={diet}
                  className="px-3 py-1 bg-emerald-100/50 text-emerald-700 rounded-full text-sm border border-emerald-200/50 flex items-center space-x-1"
                >
                  <span>{diet}</span>
                  <button
                    onClick={() => setPreferences(prev => ({
                      ...prev,
                      dietary: prev.dietary.filter(d => d !== diet)
                    }))}
                    className="ml-1 text-emerald-600 hover:text-emerald-800"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          )}

          <GlassButton
            onClick={handleGenerate}
            disabled={isGenerating || !title.trim()}
            className="w-full"
            icon={isGenerating ? RefreshCw : Sparkles}
            loading={isGenerating}
          >
            {isGenerating ? 'Generating Recipe...' : 'Generate Ingredients'}
          </GlassButton>

          {error && (
            <div className="p-3 bg-red-100/50 border border-red-200/50 rounded-lg text-red-700 text-sm">
              {error}
            </div>
          )}
        </div>
      </div>

      {/* Generated Recipe */}
      {generatedRecipe && (
        <div className="glass-card p-6 animate-float">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-gray-800">{generatedRecipe.title}</h2>
            <div className="flex space-x-2">
              <GlassButton onClick={handleSave} icon={Save} size="sm">
                Save
              </GlassButton>
              <GlassButton onClick={handleShare} icon={Share2} size="sm" variant="secondary">
                Share
              </GlassButton>
            </div>
          </div>

          {/* Recipe Info */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-3 bg-white/20 rounded-lg">
              <Users className="w-5 h-5 mx-auto mb-1 text-blue-600" />
              <div className="text-sm text-gray-600">Serves</div>
              <div className="font-semibold">{generatedRecipe.servings}</div>
            </div>
            <div className="text-center p-3 bg-white/20 rounded-lg">
              <Clock className="w-5 h-5 mx-auto mb-1 text-green-600" />
              <div className="text-sm text-gray-600">Prep Time</div>
              <div className="font-semibold">{generatedRecipe.prepTime}</div>
            </div>
            <div className="text-center p-3 bg-white/20 rounded-lg">
              <Star className="w-5 h-5 mx-auto mb-1 text-yellow-600" />
              <div className="text-sm text-gray-600">Difficulty</div>
              <div className="font-semibold">{generatedRecipe.difficulty}</div>
            </div>
            <div className="text-center p-3 bg-white/20 rounded-lg">
              <ChefHat className="w-5 h-5 mx-auto mb-1 text-purple-600" />
              <div className="text-sm text-gray-600">Cuisine</div>
              <div className="font-semibold">{generatedRecipe.cuisine}</div>
            </div>
          </div>

          {/* Ingredients */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Ingredients</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {generatedRecipe.ingredients.map((ingredient, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-3 p-3 bg-white/30 rounded-lg border border-white/40"
                >
                  <div className="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <span className="font-medium">
                      {ingredient.amount} {ingredient.unit} {ingredient.name}
                    </span>
                    {ingredient.notes && (
                      <div className="text-sm text-gray-600 mt-1">{ingredient.notes}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Dietary Info */}
          {generatedRecipe.dietaryInfo.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-semibold text-gray-700 mb-2">Dietary Information</h4>
              <div className="flex flex-wrap gap-2">
                {generatedRecipe.dietaryInfo.map((info) => (
                  <span
                    key={info}
                    className="px-2 py-1 bg-green-100/50 text-green-700 rounded text-xs border border-green-200/50"
                  >
                    {info}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Tips */}
          {generatedRecipe.tips.length > 0 && (
            <div>
              <h4 className="text-sm font-semibold text-gray-700 mb-2">Cooking Tips</h4>
              <ul className="space-y-1">
                {generatedRecipe.tips.map((tip, index) => (
                  <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                    <span className="text-emerald-500 mt-1">•</span>
                    <span>{tip}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default RecipeGenerator
