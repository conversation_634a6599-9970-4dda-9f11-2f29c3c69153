<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ViralChef Dashboard - Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom glassmorphism styles */
        .glass-card {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        .glass-sidebar {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(24px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .glass-nav {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(24px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .ai-status-card {
            transition: all 0.3s ease;
        }
        
        .ai-status-card:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .recent-activity-item {
            transition: all 0.2s ease;
        }
        
        .recent-activity-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: scale(1.02);
        }
        
        @keyframes pulse-glow {
            0%, 100% { 
                box-shadow: 0 0 5px rgba(34, 197, 94, 0.3);
            }
            50% { 
                box-shadow: 0 0 20px rgba(34, 197, 94, 0.6), 0 0 30px rgba(34, 197, 94, 0.4);
            }
        }
        
        @keyframes error-pulse {
            0%, 100% { 
                box-shadow: 0 0 5px rgba(239, 68, 68, 0.3);
            }
            50% { 
                box-shadow: 0 0 20px rgba(239, 68, 68, 0.6), 0 0 30px rgba(239, 68, 68, 0.4);
            }
        }
        
        .animate-pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite;
        }
        
        .animate-error-pulse {
            animation: error-pulse 2s ease-in-out infinite;
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-emerald-50 via-gray-50 to-blue-50">
    <!-- Header -->
    <header class="glass-nav sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-xl flex items-center justify-center">
                        🍳
                    </div>
                    <div>
                        <h1 class="text-xl font-bold gradient-text">ViralChef</h1>
                        <p class="text-xs text-gray-600">AI Recipe Generator</p>
                    </div>
                </div>

                <!-- Stats -->
                <div class="hidden md:flex items-center space-x-6">
                    <div class="text-center">
                        <div class="text-lg font-bold text-gray-800">12</div>
                        <div class="text-xs text-gray-600">Saved</div>
                    </div>
                    <div class="text-center">
                        <div class="text-lg font-bold text-gray-800">5</div>
                        <div class="text-xs text-gray-600">Today</div>
                    </div>
                    <div class="text-center">
                        <div class="text-lg font-bold text-gray-800">8</div>
                        <div class="text-xs text-gray-600">Viral</div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar Navigation - Now 350px wide -->
        <nav class="glass-sidebar w-[350px] min-h-screen p-6">
            <div class="space-y-2">
                <!-- Navigation Tabs -->
                <button class="w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 bg-gradient-to-r from-emerald-400 to-teal-500 text-white shadow-lg transform scale-105">
                    🍳 <span class="font-medium">Generate</span>
                    <span class="ml-auto">✨</span>
                </button>
                
                <button class="w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 text-gray-700 hover:bg-white/20 hover:scale-102">
                    🔍 <span class="font-medium">Viral Search</span>
                </button>
                
                <button class="w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 text-gray-700 hover:bg-white/20 hover:scale-102">
                    📚 <span class="font-medium">Saved</span>
                </button>
                
                <button class="w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 text-gray-700 hover:bg-white/20 hover:scale-102">
                    📈 <span class="font-medium">Trending</span>
                </button>
                
                <button class="w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 text-gray-700 hover:bg-white/20 hover:scale-102">
                    ⚙️ <span class="font-medium">Settings</span>
                </button>
            </div>

            <!-- AI Provider Status -->
            <div class="mt-6 pt-4 border-t border-white/20">
                <h3 class="text-sm font-semibold text-gray-600 mb-3">AI Provider Status</h3>
                <div class="ai-status-card p-3 rounded-lg border bg-green-100/50 border-green-200/50 animate-pulse-glow">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center space-x-2">
                            🧠 <span class="text-sm font-medium text-gray-700">OpenAI</span>
                        </div>
                        <span class="text-green-500">✅</span>
                    </div>
                    <div class="text-xs text-gray-600">
                        <div>Model: GPT-3.5 Turbo</div>
                        <div>Last used: 2 min ago</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mt-6 pt-4 border-t border-white/20">
                <h3 class="text-sm font-semibold text-gray-600 mb-3">Quick Actions</h3>
                <div class="space-y-2">
                    <button class="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:bg-white/10 rounded-lg transition-colors">
                        ❤️ <span>Favorites</span>
                    </button>
                    <button class="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:bg-white/10 rounded-lg transition-colors">
                        📈 <span>Popular Today</span>
                    </button>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="mt-6 pt-4 border-t border-white/20">
                <h3 class="text-sm font-semibold text-gray-600 mb-3">Recent Activity</h3>
                <div class="space-y-2">
                    <div class="recent-activity-item flex items-center space-x-2 p-2 rounded-lg transition-colors cursor-pointer">
                        🍳 
                        <div class="flex-1 min-w-0">
                            <div class="text-xs font-medium text-gray-700 truncate">Generated Pasta Recipe</div>
                            <div class="text-xs text-gray-500">2 min ago</div>
                        </div>
                    </div>
                    <div class="recent-activity-item flex items-center space-x-2 p-2 rounded-lg transition-colors cursor-pointer">
                        🔍
                        <div class="flex-1 min-w-0">
                            <div class="text-xs font-medium text-gray-700 truncate">Searched viral desserts</div>
                            <div class="text-xs text-gray-500">5 min ago</div>
                        </div>
                    </div>
                    <div class="recent-activity-item flex items-center space-x-2 p-2 rounded-lg transition-colors cursor-pointer">
                        ❤️
                        <div class="flex-1 min-w-0">
                            <div class="text-xs font-medium text-gray-700 truncate">Saved Chocolate Cake</div>
                            <div class="text-xs text-gray-500">10 min ago</div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <div class="max-w-6xl mx-auto">
                <div class="glass-card p-8 text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-xl flex items-center justify-center text-2xl">
                        🍳
                    </div>
                    <h2 class="text-3xl font-bold gradient-text mb-4">Enhanced ViralChef Dashboard</h2>
                    <p class="text-gray-600 mb-6">
                        The sidebar has been expanded to 350px and now includes:
                    </p>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-left">
                        <div class="glass-card p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">🧠 AI Provider Status</h3>
                            <p class="text-sm text-gray-600">Real-time status of your AI provider with animated indicators</p>
                        </div>
                        <div class="glass-card p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">🌐 Multiple LLM Support</h3>
                            <p class="text-sm text-gray-600">OpenAI, DeepSeek, Gemini, and OpenRouter integration</p>
                        </div>
                        <div class="glass-card p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">📊 Recent Activity</h3>
                            <p class="text-sm text-gray-600">Track your recent recipe generations and searches</p>
                        </div>
                        <div class="glass-card p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">⚙️ Advanced Settings</h3>
                            <p class="text-sm text-gray-600">Configure temperature, max tokens, and model selection</p>
                        </div>
                        <div class="glass-card p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">✨ Enhanced UI</h3>
                            <p class="text-sm text-gray-600">Improved animations and glassmorphism effects</p>
                        </div>
                        <div class="glass-card p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">🔧 Better Validation</h3>
                            <p class="text-sm text-gray-600">Smart API key validation for all providers</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
