<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ViralChef Enhanced Popup - Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0f172a, #1e293b);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .popup-container {
            width: 450px;
            height: 600px;
            background: linear-gradient(135deg, #0f172a, #1e293b, #334155);
            border-radius: 16px;
            border: 2px solid rgba(59, 130, 246, 0.3);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
            overflow: hidden;
            position: relative;
        }
        
        .demo-note {
            position: absolute;
            top: -40px;
            left: 0;
            right: 0;
            text-align: center;
            color: #94a3b8;
            font-size: 14px;
            font-weight: 600;
        }
        
        .popup-frame {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 16px;
        }
        
        .instructions {
            position: absolute;
            bottom: -120px;
            left: 0;
            right: 0;
            background: rgba(15, 23, 42, 0.9);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        
        .instructions h3 {
            color: #3b82f6;
            margin: 0 0 12px 0;
            font-size: 16px;
        }
        
        .instructions ul {
            color: #e2e8f0;
            margin: 0;
            padding-left: 20px;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .feature-highlight {
            color: #22c55e;
            font-weight: 600;
        }
        
        .warning {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin-top: 16px;
            color: #fca5a5;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="popup-container">
        <div class="demo-note">Enhanced ViralChef Popup - Demo</div>
        
        <iframe src="popup.html" class="popup-frame"></iframe>
        
        <div class="instructions">
            <h3>🚀 New Features in Enhanced Popup:</h3>
            <ul>
                <li><span class="feature-highlight">AI Status Indicator</span> - Real-time provider connection</li>
                <li><span class="feature-highlight">Tab Navigation</span> - Generate, Search, Settings</li>
                <li><span class="feature-highlight">Multiple AI Providers</span> - OpenAI, DeepSeek, Gemini, OpenRouter</li>
                <li><span class="feature-highlight">Advanced Settings</span> - Model selection, temperature control</li>
                <li><span class="feature-highlight">Enhanced UI</span> - Professional glassmorphism design</li>
                <li><span class="feature-highlight">Quick Actions</span> - Dashboard, Saved, Trending, Favorites</li>
            </ul>
            
            <div class="warning">
                <strong>Note:</strong> This is a demo version. To test the full functionality, load the extension in Chrome from the 'dist' folder.
            </div>
        </div>
    </div>
    
    <script>
        // Add some interactive demo functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('ViralChef Enhanced Popup Demo loaded! 🍳✨');
            
            // Simulate popup interactions for demo
            setTimeout(() => {
                console.log('Demo: Popup would normally connect to Chrome extension APIs');
            }, 1000);
        });
    </script>
</body>
</html>
